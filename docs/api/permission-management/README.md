# Permission Management API

This document describes the Permission Management API endpoints for loading and managing system permissions.

## Overview

The Permission Management API provides functionality to retrieve all system permissions. Permissions follow the `ROLE_ENTITY_ACTION` naming convention and are grouped by functional areas.

## 🔗 API Endpoints

### Query Permissions

#### List All Permissions
```graphql
query {
  listPermissions {
    status
    code
    dataList {
      uuid
      name
      description
      groupName
    }
    errorDescription
  }
}
```

**Required Permission**: `ROLE_PERMISSIONS_VIEW`

**Response Structure**:
```json
{
  "status": true,
  "code": "SUCCESS",
  "dataList": [
    {
      "uuid": "permission-uuid-1",
      "name": "ROLE_USERS_VIEW",
      "description": "Can view users",
      "groupName": "UAA"
    },
    {
      "uuid": "permission-uuid-2", 
      "name": "ROLE_PERMISSIONS_VIEW",
      "description": "Can view permissions",
      "groupName": "UAA"
    }
  ],
  "errorDescription": null
}
```

## 🔐 Security

### Required Permissions
- **View Permissions**: `ROLE_PERMISSIONS_VIEW` - Required to list all system permissions

### Permission Groups
Permissions are organized into logical groups:

- **UAA** - User Account and Access management
- **DASHBOARD** - Dashboard access
- **REQUESTS** - Request management
- **EXPENSES** - Expense management
- **PROFILE** - User profile management
- **SETTINGS** - System settings

## 📋 Permission Naming Convention

All permissions follow the pattern: `ROLE_ENTITY_ACTION`

### Examples:
- `ROLE_USERS_VIEW` - Can view users
- `ROLE_USERS_ADD` - Can add new users
- `ROLE_USERS_EDIT` - Can edit users
- `ROLE_REQUESTS_CREATE` - Can create requests
- `ROLE_REQUESTS_APPROVE` - Can approve requests
- `ROLE_PERMISSIONS_VIEW` - Can view permissions

## 🧪 Testing

### GraphQL Query Example
```bash
curl -X POST http://localhost:8082/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "query": "query { listPermissions { status code dataList { uuid name description groupName } } }"
  }'
```

### Expected Response
```json
{
  "data": {
    "listPermissions": {
      "status": true,
      "code": "SUCCESS",
      "dataList": [
        {
          "uuid": "generated-uuid",
          "name": "ROLE_DASHBOARD_VIEW",
          "description": "Can View Dashboard",
          "groupName": "DASHBOARD"
        },
        {
          "uuid": "generated-uuid",
          "name": "ROLE_USERS_VIEW", 
          "description": "Can View Users",
          "groupName": "UAA"
        }
      ]
    }
  }
}
```

## 🔧 Implementation Details

### Controller
- **Location**: `src/main/java/tz/co/mikesanga/approvalflow/uaa/controllers/PermissionController.java`
- **Annotations**: `@GraphQLApi`, `@Service`, `@RequiredArgsConstructor`
- **Security**: Uses `@PreAuthorize` for endpoint protection

### Service Layer
- **Interface**: `PermissionService.getAllPermissions()`
- **Implementation**: `PermissionServiceImpl.getAllPermissions()`
- **Error Handling**: Catches exceptions and returns appropriate error responses

### Response Format
- Uses `GqlResponseDto<PermissionResponseDto>` for consistent response structure
- Returns list of permissions in `dataList` field
- Includes error handling with descriptive error messages

## 📚 Related Documentation

- [API Overview](../README.md)
- [User Management API](../user-management/README.md)
- [Role Management API](../role-management/README.md)
- [Security Architecture](../../architecture/security.md)

---

*This API follows the established patterns and conventions of the Approval Flow system.*
