# Permission Management Testing Guide

This guide provides comprehensive testing scenarios for the Permission Management feature.

## Prerequisites

1. Ensure the application is running
2. Have a valid JWT token with `ROLE_PERMISSIONS_VIEW` permission
3. Access to GraphQL endpoint (typically `/graphql`)

## Test Scenarios

### 1. List All Permissions (Authorized User)

**Endpoint**: `listPermissions`
**Required Permission**: `ROLE_PERMISSIONS_VIEW`

```graphql
query {
  listPermissions {
    status
    code
    dataList {
      uuid
      name
      description
      groupName
    }
    errorDescription
  }
}
```

**Expected Result**:
- `status: true`
- `code: "SUCCESS"`
- `dataList` contains array of permissions
- Each permission has `uuid`, `name`, `description`, and `groupName`

### 2. Verify Permission Structure

Check that returned permissions follow expected patterns:

```graphql
query {
  listPermissions {
    status
    code
    dataList {
      uuid
      name
      description
      groupName
    }
  }
}
```

**Validation Checks**:
- All permission names start with `ROLE_`
- Names follow `ROLE_ENTITY_ACTION` pattern
- Group names are valid (UAA, DASH<PERSON><PERSON>D, REQUESTS, etc.)
- UUIDs are properly formatted
- Descriptions are meaningful

### 3. Check for Specific Permissions

Verify that expected permissions are present:

```graphql
query {
  listPermissions {
    dataList {
      name
      description
      groupName
    }
  }
}
```

**Expected Permissions Include**:
- `ROLE_DASHBOARD_VIEW` (DASHBOARD group)
- `ROLE_USERS_VIEW` (UAA group)
- `ROLE_USERS_ADD` (UAA group)
- `ROLE_ROLES_VIEW` (UAA group)
- `ROLE_PERMISSIONS_VIEW` (UAA group)
- `ROLE_REQUESTS_CREATE` (REQUESTS group)
- `ROLE_REQUESTS_APPROVE` (REQUESTS group)

## Error Testing Scenarios

### 1. Unauthorized Access

Try accessing without proper permission:

```graphql
query {
  listPermissions {
    status
    code
    errorDescription
  }
}
```

**Test with**:
- No JWT token
- Invalid JWT token
- Valid token but without `ROLE_PERMISSIONS_VIEW` permission

**Expected**: Access denied error

### 2. Invalid GraphQL Query

Test with malformed queries:

```graphql
query {
  listPermissions {
    invalidField
  }
}
```

**Expected**: GraphQL validation error

## Performance Testing

### 1. Response Time Test

Measure response time for permission listing:

```bash
time curl -X POST http://localhost:8082/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"query": "query { listPermissions { status dataList { name } } }"}'
```

**Expected**: Response time < 500ms

### 2. Concurrent Requests

Test multiple simultaneous requests:

```bash
# Run multiple requests in parallel
for i in {1..10}; do
  curl -X POST http://localhost:8082/graphql \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer YOUR_TOKEN" \
    -d '{"query": "query { listPermissions { status } }"}' &
done
wait
```

## Integration Testing

### 1. Permission Seeding Verification

After application startup, verify all expected permissions are seeded:

```graphql
query {
  listPermissions {
    status
    dataList {
      name
      groupName
    }
  }
}
```

**Verify**:
- All permission groups are represented
- No duplicate permissions
- All permissions from `PermissionServiceImpl.seed()` are present

### 2. Cross-Feature Integration

Test that permissions work with other features:

1. **User Management**: Verify users with `ROLE_PERMISSIONS_VIEW` can access
2. **Role Management**: Check that permissions can be assigned to roles
3. **Authentication**: Confirm JWT validation works correctly

## Automated Testing

### Unit Tests

Run the permission service unit tests:

```bash
./gradlew test --tests "*PermissionServiceImplTest*"
```

### Integration Tests

Run the permission controller integration tests:

```bash
./gradlew test --tests "*PermissionControllerTest*"
```

## Manual Testing Checklist

- [ ] Can list permissions with proper authorization
- [ ] Access denied without proper permission
- [ ] All expected permissions are present
- [ ] Permission structure is correct (uuid, name, description, groupName)
- [ ] Permission names follow naming convention
- [ ] Group names are valid
- [ ] Response format matches specification
- [ ] Error handling works correctly
- [ ] Performance is acceptable

## Common Issues and Troubleshooting

### Issue: Empty Permission List
**Cause**: Database not seeded properly
**Solution**: Check application startup logs, ensure seeding completed

### Issue: Access Denied
**Cause**: Missing `ROLE_PERMISSIONS_VIEW` permission
**Solution**: Assign permission to user's role

### Issue: Invalid JWT Token
**Cause**: Token expired or malformed
**Solution**: Re-authenticate to get fresh token

### Issue: GraphQL Errors
**Cause**: Malformed query or missing fields
**Solution**: Validate GraphQL query syntax

## Test Data

### Sample Valid Response
```json
{
  "data": {
    "listPermissions": {
      "status": true,
      "code": "SUCCESS",
      "dataList": [
        {
          "uuid": "550e8400-e29b-41d4-a716-************",
          "name": "ROLE_DASHBOARD_VIEW",
          "description": "Can View Dashboard",
          "groupName": "DASHBOARD"
        }
      ],
      "errorDescription": null
    }
  }
}
```

### Sample Error Response
```json
{
  "errors": [
    {
      "message": "Access is denied",
      "extensions": {
        "classification": "FORBIDDEN"
      }
    }
  ]
}
```

---

*For additional testing scenarios, refer to the main [Testing Guide](../../testing/README.md).*
