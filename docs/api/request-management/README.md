# Request Management API

This document describes the complete request management API functionality that has been implemented following the existing architecture patterns.

> **📋 Related Documentation:**
> - [Request Management Testing Guide](testing.md) - Comprehensive testing scenarios
> - [Implementation Details](../../implementation/README.md) - Technical implementation notes
> - [Security Model](../../architecture/security.md) - Authentication and authorization details

## Overview

The request management feature provides users with the ability to create approval requests and administrators with the functionality to approve or reject them. The system enforces proper security controls ensuring users can only manage their own requests while administrators have full oversight.

## Components Implemented

### 1. Request Entity
- **Location**: `src/main/java/tz/co/mikesanga/approvalflow/uaa/entities/Request.java`
- Extends `UaaBaseEntity<Long>` for consistent ID and timestamp management
- **Fields**:
  - `title` (required, max 200 chars)
  - `description` (optional, max 1000 chars)
  - `status` (PENDING, APPROVED, REJECTED)
  - `requestedDate` (auto-set on creation)
  - `approvedDate` (set when approved/rejected)
  - `approvalComment` (optional comment from approver)
  - `requestedBy` (ManyToOne relationship with UserAccount)
  - `approvedBy` (ManyToOne relationship with UserAccount)

### 2. Repository Layer
- **Location**: `src/main/java/tz/co/mikesanga/approvalflow/uaa/repositories/RequestRepository.java`
- Extends `JpaRepository` and `DataTablesRepository` for full CRUD and pagination support
- **Key Methods**:
  - `findByRequestedByUserId()` - Find requests by user
  - `findAllWithFilters()` - Admin view with search and status filters
  - `findByUserWithFilters()` - User view with search and status filters
  - `isRequestOwnedByUser()` - Security check for ownership
  - Various counting methods for statistics

### 3. DTOs Created
- **RequestCreateDto** - For creating and updating requests (includes optional UUID field)
- **RequestResponseDto** - For API responses with nested user information
- **RequestFilterDto** - For filtering and pagination
- **RequestApprovalDto** - For approval/rejection operations

### 4. Service Layer
- **Interface**: `src/main/java/tz/co/mikesanga/approvalflow/uaa/services/RequestService.java`
- **Implementation**: `src/main/java/tz/co/mikesanga/approvalflow/uaa/services/RequestServiceImpl.java`
- **Key Features**:
  - Full CRUD operations with proper security checks
  - Business logic for status transitions
  - User context awareness for data filtering
  - Comprehensive error handling and logging
  - Entity-to-DTO conversion

### 5. GraphQL Controller
- **Location**: `src/main/java/tz/co/mikesanga/approvalflow/uaa/controllers/RequestController.java`
- Uses `@GraphQLApi` annotation for GraphQL endpoint exposure
- **Endpoints**:
  - `saveRequest` - Save requests (create if uuid is null, update if uuid provided)
  - `getRequestByUuid` - Get single request
  - `getMyRequests` - Get user's own requests
  - `getAllRequests` - Get all requests (admin only)
  - `approveRequest` - Approve/reject requests (admin only)
  - `deleteRequest` - Delete pending requests (owner only)

### 6. Permissions Added
- **Location**: `src/main/java/tz/co/mikesanga/approvalflow/uaa/services/PermissionServiceImpl.java`
- **New Permissions**:
  - `ROLE_REQUESTS_CREATE` - Can create approval requests
  - `ROLE_REQUESTS_VIEW` - Can view own approval requests
  - `ROLE_REQUESTS_VIEW_ALL` - Can view all approval requests (admin)
  - `ROLE_REQUESTS_APPROVE` - Can approve/reject requests (admin)
  - `ROLE_REQUESTS_EDIT` - Can edit own pending requests
  - `ROLE_REQUESTS_DELETE` - Can delete own pending requests

## Security Model

### User Permissions (Non-Admin)
- **Create**: Users can create new requests
- **View**: Users can only view their own requests
- **Edit**: Users can only edit their own PENDING requests
- **Delete**: Users can only delete their own PENDING requests

### Admin Permissions
- **View All**: Admins can view all requests from all users
- **Approve/Reject**: Admins can approve or reject any PENDING request
- **Full Access**: Admins have read access to all request data

### Business Rules
1. Only PENDING requests can be updated or deleted
2. Once approved/rejected, requests become read-only
3. Users cannot approve their own requests
4. All status changes are logged with timestamps and approver information

## API Usage Examples

### Create a Request
```graphql
mutation {
  saveRequest(requestDto: {
    title: "Vacation Request"
    description: "Requesting 5 days vacation from Dec 20-24"
  }) {
    status
    code
    data {
      uuid
      title
      status
      requestedDate
      requestedBy {
        fullName
        email
      }
    }
  }
}
```

### Update a Request
```graphql
mutation {
  saveRequest(requestDto: {
    uuid: "existing-request-uuid"
    title: "Updated Vacation Request"
    description: "Updated: Requesting 7 days vacation from Dec 18-26"
  }) {
    status
    code
    data {
      uuid
      title
      status
      updatedAt
    }
  }
}
```

### Get My Requests
```graphql
query {
  getMyRequests(filter: {
    status: PENDING
    page: 0
    size: 10
    sortBy: "requestedDate"
    sortDirection: "DESC"
  }) {
    status
    code
    dataList {
      uuid
      title
      status
      requestedDate
      approvedDate
    }
    extras
  }
}
```

### Approve a Request (Admin)
```graphql
mutation {
  approveRequest(approvalDto: {
    requestUuid: "request-uuid-here"
    decision: APPROVED
    comment: "Approved for the requested dates"
  }) {
    status
    code
    data {
      uuid
      status
      approvedDate
      approvalComment
      approvedBy {
        fullName
      }
    }
  }
}
```

## Database Schema

The system will create a `requests` table with the following structure:
- `id` (Primary Key)
- `uuid` (Unique identifier)
- `title` (VARCHAR(200), NOT NULL)
- `description` (TEXT)
- `status` (ENUM: PENDING, APPROVED, REJECTED)
- `requested_date` (TIMESTAMP, NOT NULL)
- `approved_date` (TIMESTAMP)
- `approval_comment` (VARCHAR(500))
- `requested_by_id` (Foreign Key to user_accounts)
- `approved_by_id` (Foreign Key to user_accounts)
- `created_at`, `updated_at`, `deleted_at` (Inherited from UaaBaseEntity)

## Testing

The implementation includes comprehensive error handling and validation:
- Input validation using Jakarta annotations
- Business rule enforcement
- Security checks at multiple levels
- Proper transaction management
- Detailed logging for audit trails

## Integration

The feature integrates seamlessly with the existing codebase:
- Follows established naming conventions
- Uses existing security framework
- Leverages existing pagination and filtering patterns
- Maintains consistency with other GraphQL endpoints
- Respects the layered architecture pattern
