# User Management Testing Guide

This guide provides step-by-step instructions for testing the user management feature.

## Prerequisites

1. Start the application
2. Access GraphQL GUI at `http://localhost:8082/gui`
3. Obtain a JWT token by logging in with admin credentials

## Step 1: Login and Get Token

First, login to get a JWT token:

```bash
curl -X POST http://localhost:8082/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin"
  }'
```

Copy the token from the response and use it in the Authorization header for GraphQL requests.

## Step 2: Test User Creation

### GraphQL Query:
```graphql
mutation {
  createUser(userDto: {
    username: "johndo<PERSON>"
    password: "securepass123"
    fullName: "<PERSON>e"
    email: "<EMAIL>"
    phone: "1234567890"
    phoneCode: "+1"
  }) {
    status
    code
    data {
      uuid
      username
      fullName
      email
      status
      createdAt
    }
    errorDescription
  }
}
```

### Expected Response:
```json
{
  "data": {
    "createUser": {
      "status": true,
      "code": "SUCCESS",
      "data": {
        "uuid": "generated-uuid",
        "username": "johndoe",
        "fullName": "<PERSON>e",
        "email": "<EMAIL>",
        "status": "ACTIVE",
        "createdAt": "2024-01-01T10:00:00"
      },
      "errorDescription": null
    }
  }
}
```

## Step 3: Test User Listing

### GraphQL Query:
```graphql
query {
  listUsers(filter: {
    page: 0
    size: 10
    sortBy: "createdAt"
    sortDirection: "DESC"
  }) {
    status
    code
    dataList {
      uuid
      username
      fullName
      email
      status
      lastLogin
      createdAt
      roles {
        uuid
        name
        displayName
      }
    }
    extras
  }
}
```

## Step 4: Test User Update

### GraphQL Query:
```graphql
mutation {
  updateUser(userDto: {
    uuid: "user-uuid-from-step-2"
    fullName: "John Updated Doe"
    email: "<EMAIL>"
    phone: "9876543210"
    status: ACTIVE
  }) {
    status
    code
    data {
      uuid
      fullName
      email
      phone
      status
      updatedAt
    }
    errorDescription
  }
}
```

## Step 5: Test User Deactivation

### GraphQL Query:
```graphql
mutation {
  deactivateUser(uuid: "user-uuid-from-step-2") {
    status
    code
    data {
      uuid
      username
      status
      updatedAt
    }
    errorDescription
  }
}
```

## Step 6: Test User Reactivation

### GraphQL Query:
```graphql
mutation {
  reactivateUser(uuid: "user-uuid-from-step-2") {
    status
    code
    data {
      uuid
      username
      status
      updatedAt
    }
    errorDescription
  }
}
```

## Step 7: Test Role Assignment

First, get available roles:
```graphql
query {
  listRoles {
    status
    code
    dataList {
      uuid
      name
      displayName
    }
  }
}
```

Then assign roles to user:
```graphql
mutation {
  assignRolesToUser(roleAssignment: {
    userUuid: "user-uuid-from-step-2"
    roleUuids: ["role-uuid-1", "role-uuid-2"]
  }) {
    status
    code
    data {
      uuid
      username
      roles {
        uuid
        name
        displayName
      }
    }
    errorDescription
  }
}
```

## Step 8: Test Filtering and Search

### Search by name:
```graphql
query {
  listUsers(filter: {
    search: "john"
    page: 0
    size: 5
  }) {
    status
    code
    dataList {
      uuid
      username
      fullName
      email
    }
    extras
  }
}
```

### Filter by status:
```graphql
query {
  listUsers(filter: {
    status: ACTIVE
    page: 0
    size: 10
  }) {
    status
    code
    dataList {
      uuid
      username
      status
    }
    extras
  }
}
```

## Step 9: Test Error Scenarios

### Try to create user with duplicate username:
```graphql
mutation {
  createUser(userDto: {
    username: "johndoe"  # Same username as before
    password: "password123"
    fullName: "Another John"
    email: "<EMAIL>"
  }) {
    status
    code
    data {
      uuid
    }
    errorDescription
  }
}
```

Expected: `status: false`, `code: "DUPLICATE"`, `errorDescription: "Username already exists"`

### Try to get non-existent user:
```graphql
query {
  getUserByUuid(uuid: "00000000-0000-0000-0000-000000000000") {
    status
    code
    data {
      uuid
    }
    errorDescription
  }
}
```

Expected: `status: false`, `code: "NO_RECORD_FOUND"`, `errorDescription: "User not found"`

## Authorization Testing

Try accessing endpoints without proper permissions:

1. Create a user with limited permissions
2. Login with that user
3. Try to access admin endpoints
4. Should receive authorization errors

## Performance Testing

Test pagination with large datasets:

```graphql
query {
  listUsers(filter: {
    page: 0
    size: 100
    sortBy: "username"
    sortDirection: "ASC"
  }) {
    status
    code
    dataList {
      uuid
      username
    }
    extras
  }
}
```

## Validation Testing

Test input validation:

```graphql
mutation {
  createUser(userDto: {
    username: "ab"  # Too short
    password: "123"  # Too short
    fullName: ""     # Empty
    email: "invalid-email"  # Invalid format
  }) {
    status
    code
    fieldsErrors
    errorDescription
  }
}
```

Expected: Validation errors in `fieldsErrors` field.

## Notes

- Replace `user-uuid-from-step-2` with actual UUID from user creation
- Replace `role-uuid-1` and `role-uuid-2` with actual role UUIDs
- Ensure you have proper authorization headers for all requests
- Check the `extras` field in list responses for pagination information
