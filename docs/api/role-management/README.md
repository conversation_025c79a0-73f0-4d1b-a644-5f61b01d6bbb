# Role Management API

Role and permission management system for the Approval Flow API.

## 🎭 Overview

The role management system provides hierarchical access control through roles and permissions. Roles group related permissions together, and users are assigned roles to control their access to system functionality.

## 🏗️ Role System Architecture

```
Users ←→ Roles ←→ Permissions
```

- **Users**: Can have multiple roles
- **Roles**: Contain multiple permissions
- **Permissions**: Atomic access rights (e.g., `ROLE_USERS_VIEW`)

## 📋 Core Concepts

### Roles
Logical groupings of permissions that define user capabilities:
- **ADMIN**: Full system access
- **USER**: Basic user operations
- **MANAGER**: Approval capabilities
- **VIEWER**: Read-only access

### Permissions
Granular access controls following the pattern `ROLE_ENTITY_ACTION`:
- `ROLE_USERS_VIEW` - Can view users
- `ROLE_USERS_ADD` - Can create users
- `ROLE_REQUESTS_CREATE` - Can create requests
- `ROLE_REQUESTS_APPROVE` - Can approve requests

## 🔗 API Endpoints

### Query Roles
```graphql
query {
  listRoles {
    status
    code
    dataList {
      uuid
      name
      displayName
      description
      permissions {
        uuid
        name
        description
      }
    }
  }
}
```

### Get Role by UUID
```graphql
query {
  getRoleByUuid(uuid: "role-uuid-here") {
    status
    code
    data {
      uuid
      name
      displayName
      description
      permissions {
        name
        description
      }
      users {
        uuid
        username
        fullName
      }
    }
  }
}
```

### Create/Update Role
```graphql
mutation {
  saveRole(roleDto: {
    # uuid: "existing-role-uuid"  # Include for update
    name: "CUSTOM_ROLE"
    displayName: "Custom Role"
    description: "Custom role for specific users"
    permissionUuids: [
      "permission-uuid-1",
      "permission-uuid-2"
    ]
  }) {
    status
    code
    data {
      uuid
      name
      displayName
      permissions {
        name
      }
    }
  }
}
```

## 🔒 Default Roles

### Administrator Role
- **Name**: `ADMIN`
- **Display Name**: "Administrator"
- **Permissions**: All system permissions
- **Use Case**: System administrators with full access

### User Role
- **Name**: `USER`
- **Display Name**: "User"
- **Permissions**: Basic user operations
- **Use Case**: Regular users who can create and manage their own requests

### Manager Role
- **Name**: `MANAGER`
- **Display Name**: "Manager"
- **Permissions**: User permissions + approval capabilities
- **Use Case**: Users who can approve requests from other users

## 📊 Permission Categories

### User Management Permissions
- `ROLE_USERS_VIEW` - View user accounts
- `ROLE_USERS_ADD` - Create new users
- `ROLE_USERS_EDIT` - Edit existing users
- `ROLE_USERS_DEACTIVATE` - Deactivate/reactivate users

### Request Management Permissions
- `ROLE_REQUESTS_CREATE` - Create approval requests
- `ROLE_REQUESTS_VIEW` - View own requests
- `ROLE_REQUESTS_VIEW_ALL` - View all requests (admin)
- `ROLE_REQUESTS_APPROVE` - Approve/reject requests
- `ROLE_REQUESTS_EDIT` - Edit own pending requests
- `ROLE_REQUESTS_DELETE` - Delete own pending requests

### Role Management Permissions
- `ROLE_ROLES_VIEW` - View roles and permissions
- `ROLE_ROLES_ADD` - Create new roles
- `ROLE_ROLES_EDIT` - Edit existing roles
- `ROLE_ROLES_DELETE` - Delete roles

## 🛠️ Role Assignment

Roles are assigned to users through the User Management API:

```graphql
mutation {
  saveUser(userDto: {
    uuid: "user-uuid"
    username: "existinguser"
    fullName: "User Name"
    email: "<EMAIL>"
    roleUuids: ["role-uuid-1", "role-uuid-2"]
  }) {
    status
    code
    data {
      uuid
      roles {
        name
        displayName
      }
    }
  }
}
```

## 🔍 Permission Checking

### Endpoint Security
```java
@GraphQLMutation
@PreAuthorize("hasRole('ROLE_USERS_ADD')")
public GqlResponseDto<UserDto> createUser(UserCreateDto dto) {
    // Implementation
}

@GraphQLQuery
@PreAuthorize("hasAnyRole('ROLE_USERS_VIEW', 'ROLE_USERS_EDIT')")
public GqlResponseDto<List<UserDto>> listUsers(FilterDto filter) {
    // Implementation
}
```

### Service Layer Validation
```java
// Check if user has specific permission
if (!SecurityUtils.hasPermission("ROLE_REQUESTS_APPROVE")) {
    return new GqlResponseDto<>(false, ResponseCode.CUSTOM, null,
        "Insufficient permissions to approve requests");
}
```

## 🧪 Testing Role Management

### List All Roles
```bash
curl -X POST http://localhost:8082/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "query": "query { listRoles { status code dataList { name displayName permissions { name } } } }"
  }'
```

### Create Custom Role
```bash
curl -X POST http://localhost:8082/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "query": "mutation { saveRole(roleDto: { name: \"CUSTOM\", displayName: \"Custom Role\", permissionUuids: [\"perm-uuid-1\"] }) { status code data { uuid name } } }"
  }'
```

## 🔧 Role Management Best Practices

### Role Design
1. **Principle of Least Privilege**: Grant minimum necessary permissions
2. **Role Hierarchy**: Design roles with clear hierarchy
3. **Separation of Duties**: Separate conflicting permissions
4. **Regular Review**: Periodically review role assignments

### Permission Naming
1. **Consistent Format**: Use `ROLE_ENTITY_ACTION` pattern
2. **Clear Names**: Use descriptive, unambiguous names
3. **Granular Permissions**: Prefer specific over broad permissions
4. **Future-Proof**: Consider future functionality needs

### Security Considerations
1. **Default Deny**: Deny access by default, grant explicitly
2. **Role Validation**: Validate role assignments
3. **Audit Trail**: Log role and permission changes
4. **Regular Cleanup**: Remove unused roles and permissions

## 🚨 Common Issues

### Permission Denied Errors
```json
{
  "error": "Access Denied",
  "message": "User does not have required permission: ROLE_USERS_ADD"
}
```

**Solutions:**
1. Check user's assigned roles
2. Verify role contains required permission
3. Ensure permission is correctly named
4. Check endpoint security annotation

### Role Assignment Issues
- Verify role UUIDs are correct
- Check if roles exist in database
- Ensure user has permission to assign roles
- Validate role assignment in user save operation

## 📚 Related Documentation

- [User Management API](../user-management/README.md) - User and role assignment
- [Authentication API](../authentication/README.md) - JWT and permissions
- [Security Architecture](../../architecture/security.md) - Detailed security model
- [Implementation Notes](../../implementation/README.md) - Technical details

---

*The role management system provides flexible, hierarchical access control for the entire API system.*
