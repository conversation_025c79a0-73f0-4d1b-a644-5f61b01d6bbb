# Authentication API

JWT-based authentication system for the Approval Flow API.

## 🔐 Overview

The authentication system provides secure access to the Approval Flow API using JSON Web Tokens (JWT). It supports user login, token validation, and role-based access control.

## 🚀 Authentication Flow

```
1. Client sends credentials to /auth/login
2. Server validates username/password
3. Server generates JWT token with user info and roles
4. Client stores token and includes in Authorization header
5. Server validates token on each API request
6. Server checks user permissions for endpoint access
```

## 📋 Endpoints

### Login
**Endpoint**: `POST /auth/login`
**Description**: Authenticate user and receive JWT token

**Request:**
```json
{
  "username": "admin",
  "password": "admin"
}
```

**Response:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "uuid": "user-uuid",
    "username": "admin",
    "fullName": "System Administrator",
    "email": "<EMAIL>",
    "roles": [
      {
        "uuid": "role-uuid",
        "name": "<PERSON>MI<PERSON>",
        "displayName": "Administrator"
      }
    ]
  },
  "expiresIn": 86400000
}
```

### Token Validation
**Endpoint**: All GraphQL endpoints
**Description**: Automatic token validation on protected endpoints

**Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 🔒 Security Features

### JWT Token Structure
- **Header**: Algorithm and token type
- **Payload**: User information and permissions
- **Signature**: Cryptographic signature for validation

### Token Claims
```json
{
  "sub": "username",
  "userId": "user-uuid",
  "roles": ["ADMIN", "USER"],
  "permissions": ["ROLE_USERS_VIEW", "ROLE_REQUESTS_CREATE"],
  "iat": 1640995200,
  "exp": 1641081600
}
```

### Security Measures
- **Password Hashing**: BCrypt with salt
- **Token Expiration**: Configurable expiration time
- **Secure Headers**: HTTPS enforcement
- **CORS Protection**: Configurable CORS policies

## 🛠️ Configuration

### JWT Settings
```properties
# JWT Configuration
jwt.secret=your-very-secure-256-bit-secret-key-here
jwt.expiration=86400000  # 24 hours in milliseconds
jwt.header=Authorization
jwt.prefix=Bearer 
```

### Security Settings
```properties
# CORS Configuration
cors.allowed-origins=http://localhost:3000,http://localhost:4200
cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
cors.allowed-headers=*
cors.allow-credentials=true
```

## 🧪 Testing Authentication

### Login Test
```bash
curl -X POST http://localhost:8082/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin"
  }'
```

### Authenticated Request Test
```bash
curl -X POST http://localhost:8082/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{
    "query": "query { listUsers { status code } }"
  }'
```

### Token Validation Test
```bash
# Test with invalid token
curl -X POST http://localhost:8082/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer invalid-token" \
  -d '{
    "query": "query { listUsers { status code } }"
  }'
```

## 🚨 Error Handling

### Authentication Errors
- **401 Unauthorized**: Invalid credentials or missing token
- **403 Forbidden**: Valid token but insufficient permissions
- **400 Bad Request**: Malformed request

### Error Responses
```json
{
  "error": "Unauthorized",
  "message": "Invalid credentials",
  "timestamp": "2024-01-01T10:00:00Z",
  "path": "/auth/login"
}
```

## 🔧 Implementation Details

### Password Security
- BCrypt hashing with configurable rounds
- Salt generation for each password
- Secure password validation

### Token Management
- Stateless JWT tokens
- Configurable expiration
- Automatic token validation
- Role and permission embedding

### Session Management
- No server-side sessions
- Client-side token storage
- Token refresh capabilities (future enhancement)

## 📚 Related Documentation

- [User Management API](../user-management/README.md) - User account management
- [Security Architecture](../../architecture/security.md) - Detailed security model
- [Setup Guide](../../setup/README.md) - Authentication configuration
- [Troubleshooting](../../implementation/troubleshooting.md) - Authentication issues

---

*The authentication system provides secure, stateless access control for the entire API system.*
