# Development Patterns

Established coding patterns and conventions used throughout the Approval Flow API codebase.

## 🏗️ Architectural Patterns

### 1. Layered Architecture Pattern
The codebase follows a strict layered architecture with clear separation of concerns:

```
Controllers → Services → Repositories → Entities
```

#### Controller Layer
```java
@GraphQLApi
@Service
@Slf4j
public class EntityController {
    
    @Autowired
    private EntityService entityService;
    
    @GraphQLMutation
    @PreAuthorize("hasAnyRole('ROLE_ENTITY_ADD', 'ROLE_ENTITY_EDIT')")
    public GqlResponseDto<EntityDto> saveEntity(EntityCreateDto dto) {
        return entityService.saveEntity(dto);
    }
}
```

**Responsibilities:**
- Handle GraphQL requests and responses
- Apply security annotations (`@PreAuthorize`)
- Delegate business logic to services
- Input validation and error handling

#### Service Layer
```java
@Service
@Transactional
@Slf4j
public class EntityServiceImpl implements EntityService {
    
    @Autowired
    private EntityRepository repository;
    
    public GqlResponseDto<EntityDto> saveEntity(EntityCreateDto dto) {
        // Business logic implementation
        // Transaction management
        // Entity-to-DTO conversion
    }
}
```

**Responsibilities:**
- Implement business logic
- Manage transactions
- Perform validation
- Convert between entities and DTOs

#### Repository Layer
```java
@Repository
public interface EntityRepository extends JpaRepository<Entity, Long>, 
                                         DataTablesRepository<Entity, Long> {
    
    Optional<Entity> findFirstByUuid(UUID uuid);
    
    @Query("SELECT e FROM Entity e WHERE ...")
    Page<Entity> findWithFilters(Pageable pageable);
}
```

**Responsibilities:**
- Data access operations
- Custom queries
- Pagination support

### 2. Unified Save Pattern
Single method handles both create and update operations based on UUID presence:

```java
public GqlResponseDto<EntityDto> saveEntity(EntityCreateDto dto) {
    // Check if this is an update operation
    Optional<Entity> optionalEntity = getOptionalByUuid(dto.getUuid());
    
    if (dto.getUuid() != null && optionalEntity.isEmpty()) {
        return new GqlResponseDto<>(false, ResponseCode.CUSTOM, null, 
            "Entity not found or may have been removed.");
    }
    
    Entity entity = optionalEntity.orElse(new Entity());
    
    if (dto.getUuid() != null) {
        // Update existing entity
        updateEntityFields(entity, dto);
        log.info("Updating entity with UUID: {}", dto.getUuid());
    } else {
        // Create new entity
        setEntityFields(entity, dto);
        log.info("Creating new entity");
    }
    
    // Common save logic
    Entity savedEntity = repository.save(entity);
    return new GqlResponseDto<>(true, ResponseCode.SUCCESS, 
        convertToDto(savedEntity), null);
}

private Optional<Entity> getOptionalByUuid(String uuid) {
    if (uuid == null || uuid.trim().isEmpty()) {
        return Optional.empty();
    }
    return repository.findFirstByUuid(UUID.fromString(uuid));
}
```

**Benefits:**
- Single endpoint for both operations
- Consistent API design
- Reduced code duplication
- Atomic transactions

## 🔒 Security Patterns

### 1. Permission Naming Convention
```java
// Format: ROLE_ENTITY_ACTION
public static final String ROLE_USERS_VIEW = "ROLE_USERS_VIEW";
public static final String ROLE_USERS_ADD = "ROLE_USERS_ADD";
public static final String ROLE_USERS_EDIT = "ROLE_USERS_EDIT";
public static final String ROLE_REQUESTS_CREATE = "ROLE_REQUESTS_CREATE";
public static final String ROLE_REQUESTS_APPROVE = "ROLE_REQUESTS_APPROVE";
```

### 2. Endpoint Security Pattern
```java
@GraphQLMutation
@PreAuthorize("hasAnyRole('ROLE_ENTITY_ADD', 'ROLE_ENTITY_EDIT')")
public GqlResponseDto<EntityDto> saveEntity(EntityCreateDto dto) {
    // Implementation
}

@GraphQLQuery
@PreAuthorize("hasRole('ROLE_ENTITY_VIEW')")
public GqlResponseDto<List<EntityDto>> listEntities(FilterDto filter) {
    // Implementation
}
```

### 3. Data Access Security Pattern
```java
// In service layer - check ownership
public GqlResponseDto<RequestDto> updateRequest(RequestCreateDto dto) {
    Optional<Request> requestOpt = getOptionalByUuid(dto.getUuid());
    
    if (requestOpt.isPresent()) {
        Request request = requestOpt.get();
        String currentUsername = SecurityContextHolder.getContext()
            .getAuthentication().getName();
        
        // Ensure user can only update their own requests
        if (!request.getRequestedBy().getUsername().equals(currentUsername)) {
            return new GqlResponseDto<>(false, ResponseCode.CUSTOM, null,
                "You can only update your own requests");
        }
    }
    
    // Continue with update logic
}
```

## 📊 Data Patterns

### 1. Entity Base Pattern
```java
@Entity
@Table(name = "entity_name")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EntityName extends UaaBaseEntity<Long> {
    
    @Column(nullable = false, length = 200)
    @NotBlank(message = "Field is required")
    private String requiredField;
    
    @Column(length = 1000)
    private String optionalField;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private EntityStatus status = EntityStatus.ACTIVE;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    @JsonIgnore
    private UserAccount user;
}
```

**Key Elements:**
- Extend `UaaBaseEntity<Long>` for ID and timestamps
- Use appropriate column constraints
- Apply validation annotations
- Use `@JsonIgnore` for sensitive relationships
- Set sensible defaults

### 2. DTO Pattern
```java
// Request DTO
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EntityCreateDto {
    private String uuid; // Optional for create/update pattern
    
    @NotBlank(message = "Title is required")
    @Size(max = 200, message = "Title must not exceed 200 characters")
    private String title;
    
    @Size(max = 1000, message = "Description must not exceed 1000 characters")
    private String description;
    
    private EntityStatus status;
    
    // For relationships
    private List<String> relatedUuids;
}

// Response DTO
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EntityResponseDto {
    private String uuid;
    private String title;
    private String description;
    private EntityStatus status;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Nested DTOs for relationships
    private UserResponseDto createdBy;
    private List<RelatedEntityDto> relatedEntities;
}
```

### 3. Repository Query Pattern
```java
@Repository
public interface EntityRepository extends JpaRepository<Entity, Long>, 
                                         DataTablesRepository<Entity, Long> {
    
    // Standard UUID lookup
    Optional<Entity> findFirstByUuid(UUID uuid);
    
    // User-specific queries
    @Query("SELECT e FROM Entity e WHERE e.user.id = :userId")
    List<Entity> findByUserId(@Param("userId") Long userId);
    
    // Complex filtering with pagination
    @Query("SELECT e FROM Entity e WHERE " +
           "(:search IS NULL OR :search = '' OR " +
           "UPPER(e.title) LIKE UPPER(CONCAT('%', :search, '%'))) AND " +
           "(:status IS NULL OR e.status = :status)")
    Page<Entity> findWithFilters(@Param("search") String search,
                                @Param("status") EntityStatus status,
                                Pageable pageable);
    
    // Ownership validation
    @Query("SELECT COUNT(e) > 0 FROM Entity e WHERE e.uuid = :uuid AND e.user.id = :userId")
    boolean isOwnedByUser(@Param("uuid") UUID uuid, @Param("userId") Long userId);
}
```

## 🔄 Response Patterns

### 1. Consistent Response Format
```java
// Success response
return new GqlResponseDto<>(
    true,                    // status
    ResponseCode.SUCCESS,    // code
    responseData,           // data
    null                    // errorDescription
);

// Error response
return new GqlResponseDto<>(
    false,                   // status
    ResponseCode.CUSTOM,     // code
    null,                   // data
    "Error message"         // errorDescription
);

// Validation error response
return new GqlResponseDto<>(
    false,                   // status
    ResponseCode.INVALID_REQUEST, // code
    null,                   // data
    "Validation failed",    // errorDescription
    validationErrors        // fieldsErrors
);
```

### 2. List Response Pattern
```java
public GqlResponseDto<List<EntityDto>> listEntities(FilterDto filter) {
    try {
        Pageable pageable = PageRequest.of(
            filter.getPage(),
            filter.getSize(),
            Sort.by(Sort.Direction.fromString(filter.getSortDirection()),
                   filter.getSortBy())
        );
        
        Page<Entity> page = repository.findWithFilters(
            filter.getSearch(),
            filter.getStatus(),
            pageable
        );
        
        List<EntityDto> dtos = page.getContent().stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());
        
        // Pagination info in extras
        Map<String, Object> extras = new HashMap<>();
        extras.put("totalElements", page.getTotalElements());
        extras.put("totalPages", page.getTotalPages());
        extras.put("currentPage", page.getNumber());
        extras.put("pageSize", page.getSize());
        
        return new GqlResponseDto<>(true, ResponseCode.SUCCESS, dtos, null, extras);
        
    } catch (Exception e) {
        log.error("Error listing entities", e);
        return new GqlResponseDto<>(false, ResponseCode.CUSTOM, null,
            "Failed to retrieve entities");
    }
}
```

## 🧪 Testing Patterns

### 1. Service Layer Testing
```java
@ExtendWith(MockitoExtension.class)
class EntityServiceImplTest {
    
    @Mock
    private EntityRepository repository;
    
    @Mock
    private UserRepository userRepository;
    
    @InjectMocks
    private EntityServiceImpl service;
    
    @Test
    void saveEntity_CreateNew_Success() {
        // Given
        EntityCreateDto dto = new EntityCreateDto();
        dto.setTitle("Test Entity");
        // dto.setUuid(null); // Create operation
        
        Entity savedEntity = new Entity();
        savedEntity.setUuid(UUID.randomUUID());
        savedEntity.setTitle("Test Entity");
        
        when(repository.save(any(Entity.class))).thenReturn(savedEntity);
        
        // When
        GqlResponseDto<EntityDto> result = service.saveEntity(dto);
        
        // Then
        assertTrue(result.getStatus());
        assertEquals(ResponseCode.SUCCESS, result.getCode());
        assertNotNull(result.getData());
        assertEquals("Test Entity", result.getData().getTitle());
    }
    
    @Test
    void saveEntity_UpdateExisting_Success() {
        // Test update scenario
    }
    
    @Test
    void saveEntity_ValidationError_ReturnsError() {
        // Test validation failure
    }
}
```

### 2. Integration Testing Pattern
```java
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@TestPropertySource(locations = "classpath:application-test.properties")
class EntityControllerIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void saveEntity_WithValidData_ReturnsSuccess() {
        // Setup test data
        // Execute GraphQL mutation
        // Verify response
        // Check database state
    }
}
```

## 📝 Logging Patterns

### 1. Service Layer Logging
```java
@Service
@Slf4j
public class EntityServiceImpl implements EntityService {
    
    public GqlResponseDto<EntityDto> saveEntity(EntityCreateDto dto) {
        if (dto.getUuid() != null) {
            log.info("Updating entity with UUID: {}", dto.getUuid());
        } else {
            log.info("Creating new entity with title: {}", dto.getTitle());
        }
        
        try {
            // Business logic
            log.debug("Entity saved successfully: {}", savedEntity.getUuid());
            return new GqlResponseDto<>(true, ResponseCode.SUCCESS, responseDto, null);
            
        } catch (Exception e) {
            log.error("Error saving entity: {}", e.getMessage(), e);
            return new GqlResponseDto<>(false, ResponseCode.CUSTOM, null,
                "Failed to save entity");
        }
    }
}
```

### 2. Security Logging
```java
// Log authentication events
log.info("User {} successfully authenticated", username);
log.warn("Failed authentication attempt for user: {}", username);

// Log authorization events
log.debug("User {} accessing endpoint: {}", username, endpoint);
log.warn("Access denied for user {} to endpoint: {}", username, endpoint);
```

## 📚 Additional Resources

- [Development Guide](README.md) - Complete development setup
- [API Documentation](../api/README.md) - API reference
- [Architecture Overview](../architecture/README.md) - System architecture
- [Implementation Notes](../implementation/README.md) - Technical details

---

*Following these patterns ensures consistency, maintainability, and scalability across the codebase.*
