# Setup Guide

Complete installation and configuration guide for the Approval Flow GraphQL API system.

## 🚀 Quick Start

### Prerequisites
- **Java 17+** (OpenJDK or Oracle JDK)
- **PostgreSQL 12+** 
- **Git** for version control
- **Gradle** (included via wrapper)

### Installation Steps

1. **Clone the Repository**
   ```bash
   git clone <repository-url>
   cd approval-flow-be
   ```

2. **Database Setup**
   ```bash
   # Create PostgreSQL database
   createdb approval_flow
   
   # Create user (optional)
   psql -c "CREATE USER approval_user WITH PASSWORD 'your_password';"
   psql -c "GRANT ALL PRIVILEGES ON DATABASE approval_flow TO approval_user;"
   ```

3. **Configuration**
   ```bash
   # Configure application properties
   cp src/main/resources/application.properties.example src/main/resources/application.properties
   ```
   
   Edit `application.properties`:
   ```properties
   spring.datasource.url=**********************************************
   spring.datasource.username=approval_user
   spring.datasource.password=your_password
   jwt.secret=your-256-bit-secret-key
   ```

4. **Build and Run**
   ```bash
   # Build the application
   ./gradlew build
   
   # Run the application
   ./gradlew bootRun
   ```

5. **Verify Installation**
   - **API Endpoint**: `http://localhost:8082/graphql`
   - **GraphQL GUI**: `http://localhost:8082/gui`
   - **Health Check**: `http://localhost:8082/actuator/health`

## 🔧 Detailed Configuration

### Database Configuration

#### PostgreSQL Setup
```sql
-- Create database
CREATE DATABASE approval_flow;

-- Create user
CREATE USER approval_user WITH PASSWORD 'secure_password';

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE approval_flow TO approval_user;
GRANT ALL ON SCHEMA public TO approval_user;
```

#### Connection Properties
```properties
# Database Configuration
spring.datasource.url=**********************************************
spring.datasource.username=approval_user
spring.datasource.password=secure_password
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=true
```

### Security Configuration

#### JWT Settings
```properties
# JWT Configuration
jwt.secret=your-very-secure-256-bit-secret-key-here
jwt.expiration=86400000
jwt.header=Authorization
jwt.prefix=Bearer 
```

#### CORS Configuration
```properties
# CORS Settings (for frontend integration)
cors.allowed-origins=http://localhost:3000,http://localhost:4200
cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
cors.allowed-headers=*
cors.allow-credentials=true
```

### Application Configuration

#### Server Settings
```properties
# Server Configuration
server.port=8082
server.servlet.context-path=/

# GraphQL Configuration
graphql.servlet.mapping=/graphql
graphql.servlet.enabled=true
graphql.servlet.cors-enabled=true
```

#### Logging Configuration
```properties
# Logging Configuration
logging.level.tz.co.mikesanga.approvalflow=INFO
logging.level.org.springframework.security=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
```

## 🐳 Docker Setup

### Docker Compose
Create `docker-compose.yml`:
```yaml
version: '3.8'
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: approval_flow
      POSTGRES_USER: approval_user
      POSTGRES_PASSWORD: secure_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  app:
    build: .
    ports:
      - "8082:8082"
    depends_on:
      - postgres
    environment:
      SPRING_DATASOURCE_URL: *********************************************
      SPRING_DATASOURCE_USERNAME: approval_user
      SPRING_DATASOURCE_PASSWORD: secure_password
      JWT_SECRET: your-very-secure-256-bit-secret-key-here

volumes:
  postgres_data:
```

### Dockerfile
```dockerfile
FROM openjdk:17-jdk-slim

WORKDIR /app

COPY gradle gradle
COPY gradlew .
COPY build.gradle .
COPY settings.gradle .

RUN ./gradlew dependencies

COPY src src

RUN ./gradlew build -x test

EXPOSE 8082

CMD ["java", "-jar", "build/libs/approval-flow-0.0.1-SNAPSHOT.jar"]
```

### Running with Docker
```bash
# Start services
docker-compose up -d

# View logs
docker-compose logs -f app

# Stop services
docker-compose down
```

## 🌍 Environment-Specific Setup

### Development Environment
```properties
# application-dev.properties
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
logging.level.tz.co.mikesanga.approvalflow=DEBUG
```

### Testing Environment
```properties
# application-test.properties
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driver-class-name=org.h2.Driver
spring.jpa.hibernate.ddl-auto=create-drop
```

### Production Environment
```properties
# application-prod.properties
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false
logging.level.tz.co.mikesanga.approvalflow=WARN
```

## 🔐 Initial Data Setup

### Default Admin User
The system creates a default admin user on first startup:
- **Username**: `admin`
- **Password**: `admin`
- **Roles**: All administrative permissions

### Changing Default Credentials
```bash
# Login and change password immediately
curl -X POST http://localhost:8082/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin"}'

# Use returned token to change password
curl -X POST http://localhost:8082/graphql \
  -H "Authorization: Bearer <token>" \
  -d '{"query": "mutation { changePassword(oldPassword: \"admin\", newPassword: \"new_secure_password\") { status } }"}'
```

## 🔍 Verification and Testing

### Health Checks
```bash
# Application health
curl http://localhost:8082/actuator/health

# Database connectivity
curl http://localhost:8082/actuator/health/db
```

### GraphQL Testing
```bash
# Test GraphQL endpoint
curl -X POST http://localhost:8082/graphql \
  -H "Content-Type: application/json" \
  -d '{"query": "query { __schema { types { name } } }"}'
```

### Authentication Testing
```bash
# Get JWT token
curl -X POST http://localhost:8082/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin"}'

# Test authenticated endpoint
curl -X POST http://localhost:8082/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <your-token>" \
  -d '{"query": "query { listUsers { status code } }"}'
```

## 🚨 Troubleshooting

### Common Issues

#### Database Connection Failed
```bash
# Check PostgreSQL is running
sudo systemctl status postgresql

# Check connection
psql -h localhost -U approval_user -d approval_flow
```

#### Port Already in Use
```bash
# Find process using port 8082
lsof -i :8082

# Kill process
kill -9 <PID>
```

#### JWT Token Issues
- Ensure JWT secret is at least 256 bits
- Check token expiration settings
- Verify Authorization header format: `Bearer <token>`

#### Permission Denied
- Check database user permissions
- Verify file permissions for application.properties
- Ensure Java version compatibility

### Log Analysis
```bash
# View application logs
tail -f logs/application.log

# Check for specific errors
grep -i error logs/application.log

# Monitor database queries
grep -i "hibernate.SQL" logs/application.log
```

## 📚 Next Steps

After successful setup:
1. **[API Documentation](../api/README.md)** - Learn about available endpoints
2. **[Testing Guide](../testing/README.md)** - Test your installation
3. **[Development Guide](../development/README.md)** - Start developing
4. **[Security Configuration](../architecture/security.md)** - Secure your installation

## 🆘 Support

For additional help:
- Check [Troubleshooting Guide](../implementation/troubleshooting.md)
- Review [Architecture Documentation](../architecture/README.md)
- See [Implementation Notes](../implementation/README.md)

---

*Your Approval Flow API is now ready to use! 🎉*
