# Troubleshooting Guide

Common issues and solutions for the Approval Flow API system.

## 🚨 Common Issues

### Database Connection Issues

#### Problem: "Connection refused" or "Database connection failed"
**Symptoms:**
- Application fails to start
- Error: `org.postgresql.util.PSQLException: Connection refused`

**Solutions:**
1. **Check PostgreSQL Service**
   ```bash
   # Check if PostgreSQL is running
   sudo systemctl status postgresql
   
   # Start PostgreSQL if not running
   sudo systemctl start postgresql
   ```

2. **Verify Database Exists**
   ```bash
   # Connect to PostgreSQL
   psql -U postgres
   
   # List databases
   \l
   
   # Create database if missing
   CREATE DATABASE approval_flow;
   ```

3. **Check Connection Parameters**
   ```properties
   # Verify in application.properties
   spring.datasource.url=**********************************************
   spring.datasource.username=your_username
   spring.datasource.password=your_password
   ```

#### Problem: "Authentication failed for user"
**Solutions:**
1. **Reset User Password**
   ```sql
   ALTER USER approval_user WITH PASSWORD 'new_password';
   ```

2. **Grant Proper Permissions**
   ```sql
   GRANT ALL PRIVILEGES ON DATABASE approval_flow TO approval_user;
   GRANT ALL ON SCHEMA public TO approval_user;
   ```

### Authentication Issues

#### Problem: "Invalid JWT token" or "Token expired"
**Symptoms:**
- 401 Unauthorized responses
- "JWT signature does not match locally computed signature"

**Solutions:**
1. **Check JWT Secret**
   ```properties
   # Ensure JWT secret is at least 256 bits (32 characters)
   jwt.secret=your-very-secure-256-bit-secret-key-here
   ```

2. **Verify Token Format**
   ```bash
   # Correct Authorization header format
   Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
   ```

3. **Check Token Expiration**
   ```properties
   # Adjust token expiration (in milliseconds)
   jwt.expiration=86400000  # 24 hours
   ```

#### Problem: "Access Denied" or "Insufficient permissions"
**Solutions:**
1. **Check User Roles**
   ```graphql
   query {
     getUserByUuid(uuid: "user-uuid") {
       roles {
         name
         permissions {
           name
         }
       }
     }
   }
   ```

2. **Verify Permission Requirements**
   ```java
   // Check @PreAuthorize annotation on endpoint
   @PreAuthorize("hasRole('ROLE_USERS_VIEW')")
   ```

3. **Assign Missing Roles**
   ```graphql
   mutation {
     saveUser(userDto: {
       uuid: "user-uuid"
       roleUuids: ["admin-role-uuid"]
     }) {
       status
     }
   }
   ```

### GraphQL Issues

#### Problem: "Schema validation error" or "Field not found"
**Solutions:**
1. **Check GraphQL Schema**
   ```bash
   # Access GraphQL GUI to explore schema
   http://localhost:8082/gui
   ```

2. **Verify Field Names**
   ```graphql
   # Use introspection query
   query {
     __schema {
       types {
         name
         fields {
           name
           type {
             name
           }
         }
       }
     }
   }
   ```

#### Problem: "Validation failed" or "Required field missing"
**Solutions:**
1. **Check DTO Validation**
   ```java
   // Ensure required fields are provided
   @NotBlank(message = "Username is required")
   private String username;
   ```

2. **Review Error Response**
   ```json
   {
     "status": false,
     "fieldsErrors": {
       "username": ["Username is required"]
     }
   }
   ```

### Performance Issues

#### Problem: Slow query performance
**Solutions:**
1. **Enable SQL Logging**
   ```properties
   spring.jpa.show-sql=true
   spring.jpa.properties.hibernate.format_sql=true
   logging.level.org.hibernate.SQL=DEBUG
   ```

2. **Add Database Indexes**
   ```sql
   -- Add indexes on frequently queried columns
   CREATE INDEX idx_user_username ON user_accounts(username);
   CREATE INDEX idx_request_status ON requests(status);
   ```

3. **Optimize Queries**
   ```java
   // Use pagination for large datasets
   @Query("SELECT u FROM UserAccount u WHERE ...")
   Page<UserAccount> findWithPagination(Pageable pageable);
   ```

#### Problem: High memory usage
**Solutions:**
1. **Configure JVM Options**
   ```bash
   # Set appropriate heap size
   java -Xmx2g -Xms1g -jar approval-flow.jar
   ```

2. **Optimize Entity Loading**
   ```java
   // Use LAZY loading for relationships
   @ManyToOne(fetch = FetchType.LAZY)
   private UserAccount user;
   ```

### Application Startup Issues

#### Problem: "Port already in use"
**Solutions:**
1. **Find Process Using Port**
   ```bash
   # Find process on port 8082
   lsof -i :8082
   
   # Kill process
   kill -9 <PID>
   ```

2. **Change Application Port**
   ```properties
   server.port=8083
   ```

#### Problem: "Bean creation failed" or "Dependency injection error"
**Solutions:**
1. **Check Component Scanning**
   ```java
   @SpringBootApplication
   @ComponentScan(basePackages = "tz.co.mikesanga.approvalflow")
   public class ApprovalFlowApplication {
   ```

2. **Verify Bean Dependencies**
   ```java
   // Ensure all required dependencies are available
   @Autowired
   private UserRepository userRepository;
   ```

## 🔧 Debugging Tools

### Application Logs
```bash
# View real-time logs
tail -f logs/application.log

# Search for errors
grep -i error logs/application.log

# Filter by component
grep "UserManagementService" logs/application.log
```

### Database Debugging
```sql
-- Check table structure
\d user_accounts

-- View recent data
SELECT * FROM user_accounts ORDER BY created_at DESC LIMIT 10;

-- Check constraints
SELECT * FROM information_schema.table_constraints 
WHERE table_name = 'user_accounts';
```

### GraphQL Debugging
```bash
# Test GraphQL endpoint
curl -X POST http://localhost:8082/graphql \
  -H "Content-Type: application/json" \
  -d '{"query": "query { __schema { types { name } } }"}'
```

## 📊 Health Checks

### Application Health
```bash
# Basic health check
curl http://localhost:8082/actuator/health

# Database health
curl http://localhost:8082/actuator/health/db

# Detailed health info
curl http://localhost:8082/actuator/health/details
```

### System Resources
```bash
# Check memory usage
free -h

# Check disk space
df -h

# Check CPU usage
top
```

## 🆘 Getting Help

### Log Analysis
1. **Enable Debug Logging**
   ```properties
   logging.level.tz.co.mikesanga.approvalflow=DEBUG
   logging.level.org.springframework.security=DEBUG
   ```

2. **Collect Relevant Information**
   - Application version
   - Error messages and stack traces
   - Configuration files
   - Database schema version

### Support Checklist
- [ ] Check this troubleshooting guide
- [ ] Review application logs
- [ ] Verify configuration settings
- [ ] Test with minimal setup
- [ ] Check database connectivity
- [ ] Validate JWT tokens
- [ ] Confirm user permissions

## 📚 Additional Resources

- [Setup Guide](../setup/README.md) - Installation and configuration
- [Development Guide](../development/README.md) - Development environment setup
- [API Documentation](../api/README.md) - Complete API reference
- [Architecture Overview](../architecture/README.md) - System architecture

---

*If you encounter issues not covered here, please check the logs and configuration first, then consult the relevant documentation sections.*
