package tz.co.mikesanga.approvalflow.global.requests.entities;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.*;
import tz.co.mikesanga.approvalflow.uaa.entities.UaaBaseEntity;
import tz.co.mikesanga.approvalflow.uaa.entities.UserAccount;

import java.time.LocalDateTime;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Builder
@Table(name = "requests")
public class Request extends UaaBaseEntity<Long> {

    @NotBlank(message = "Title is required")
    @Size(max = 200, message = "Title cannot exceed 200 characters")
    @Column(nullable = false, length = 200)
    private String title;

    @Size(max = 1000, message = "Description cannot exceed 1000 characters")
    @Column(length = 1000)
    private String description;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @Builder.Default
    private RequestStatus status = RequestStatus.PENDING;

    @Column(nullable = false)
    @Builder.Default
    private LocalDateTime requestedDate = LocalDateTime.now();

    private LocalDateTime approvedDate;

    @Size(max = 500, message = "Approval comment cannot exceed 500 characters")
    @Column(columnDefinition = "varchar(500)")
    private String approvalComment;

    // Relationship with the user who created the request
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "requested_by_id", nullable = false)
    @JsonIgnore
    private UserAccount requestedBy;

    // Relationship with the user who approved/rejected the request
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "approved_by_id")
    @JsonIgnore
    private UserAccount approvedBy;

    public enum RequestStatus {
        PENDING, APPROVED, REJECTED
    }
}
