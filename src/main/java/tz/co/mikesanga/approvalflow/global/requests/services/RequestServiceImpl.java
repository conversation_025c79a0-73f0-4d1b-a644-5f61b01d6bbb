package tz.co.mikesanga.approvalflow.global.requests.services;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tz.co.mikesanga.approvalflow.global.dtos.GqlResponseDto;
import tz.co.mikesanga.approvalflow.global.dtos.ResponseCode;
import tz.co.mikesanga.approvalflow.global.requests.dtos.RequestApprovalDto;
import tz.co.mikesanga.approvalflow.global.requests.dtos.RequestCreateDto;
import tz.co.mikesanga.approvalflow.global.requests.dtos.RequestFilterDto;
import tz.co.mikesanga.approvalflow.global.requests.dtos.RequestResponseDto;
import tz.co.mikesanga.approvalflow.security.UserPrincipal;
import tz.co.mikesanga.approvalflow.global.requests.entities.Request;
import tz.co.mikesanga.approvalflow.uaa.entities.UserAccount;
import tz.co.mikesanga.approvalflow.global.requests.repositories.RequestRepository;
import tz.co.mikesanga.approvalflow.uaa.repositories.UserAccountRepository;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class RequestServiceImpl implements RequestService {

    private final RequestRepository requestRepository;
    private final UserAccountRepository userAccountRepository;

    @Override
    @Transactional
    public GqlResponseDto<RequestResponseDto> saveRequest(RequestCreateDto requestDto, UserPrincipal userPrincipal) {
        try {
            log.info("Saving request for user: {}", userPrincipal.getUsername());

            // Get the current user
            Optional<UserAccount> userOpt = userAccountRepository.findById(userPrincipal.getUserId());
            if (userOpt.isEmpty()) {
                return new GqlResponseDto<>(false, ResponseCode.UNAUTHORIZED, null, "User not found");
            }
            UserAccount currentUser = userOpt.get();

            // Check if this is an update operation (UUID provided)
            Optional<Request> optionalRequest = getOptionalByUuid(requestDto.getUuid());
            if (requestDto.getUuid() != null && optionalRequest.isEmpty()) {
                return new GqlResponseDto<>(false, ResponseCode.CUSTOM, null, "Request edit failed: The request was not found or may have been removed.");
            }

            Request request = optionalRequest.orElse(new Request());

            // For update operations, validate ownership and status
            if (requestDto.getUuid() != null) {
                // Check if user owns the request
                if (!request.getRequestedBy().getId().equals(userPrincipal.getUserId())) {
                    return new GqlResponseDto<>(false, ResponseCode.UNAUTHORIZED, null, "You can only update your own requests");
                }

                // Check if request is still pending
                if (request.getStatus() != Request.RequestStatus.PENDING) {
                    return new GqlResponseDto<>(false, ResponseCode.CUSTOM, null, "Only pending requests can be updated");
                }

                log.info("Updating existing request with UUID: {}", requestDto.getUuid());
                request.setUpdatedAt(LocalDateTime.now());
            } else {
                // This is a create operation
                log.info("Creating new request for user: {}", userPrincipal.getUsername());
                request.setStatus(Request.RequestStatus.PENDING);
                request.setRequestedDate(LocalDateTime.now());
                request.setRequestedBy(currentUser);
            }

            // Set common fields
            request.setTitle(requestDto.getTitle());
            request.setDescription(requestDto.getDescription());

            Request savedRequest = requestRepository.save(request);
            log.info("Request saved successfully with ID: {}", savedRequest.getId());

            return new GqlResponseDto<>(true, ResponseCode.SUCCESS, convertToResponseDto(savedRequest));

        } catch (Exception e) {
            log.error("Error saving request: ", e);
            return new GqlResponseDto<>(false, ResponseCode.FAILURE, null, "Error saving request: " + e.getMessage());
        }
    }



    @Override
    public GqlResponseDto<RequestResponseDto> getRequestByUuid(String uuid, UserPrincipal userPrincipal) {
        try {
            log.info("Fetching request with UUID: {} by user: {}", uuid, userPrincipal.getUsername());

            Optional<Request> requestOpt = requestRepository.findFirstByUuid(UUID.fromString(uuid));
            if (requestOpt.isEmpty()) {
                return new GqlResponseDto<>(false, ResponseCode.NO_RECORD_FOUND, null, "Request not found");
            }

            Request request = requestOpt.get();

            // Check if user can view this request (owner or admin)
            boolean isOwner = request.getRequestedBy().getId().equals(userPrincipal.getUserId());
            boolean isAdmin = userPrincipal.getAuthorities().stream()
                    .anyMatch(auth -> auth.getAuthority().equals("ROLE_REQUESTS_VIEW_ALL"));

            if (!isOwner && !isAdmin) {
                return new GqlResponseDto<>(false, ResponseCode.UNAUTHORIZED, null, "You can only view your own requests");
            }

            return new GqlResponseDto<>(true, ResponseCode.SUCCESS, convertToResponseDto(request));

        } catch (Exception e) {
            log.error("Error fetching request: ", e);
            return new GqlResponseDto<>(false, ResponseCode.FAILURE, null, "Error fetching request: " + e.getMessage());
        }
    }

    @Override
    public GqlResponseDto<RequestResponseDto> getMyRequests(RequestFilterDto filterDto, UserPrincipal userPrincipal) {
        try {
            log.info("Fetching requests for user: {} with filters: {}", userPrincipal.getUsername(), filterDto);

            Sort sort = Sort.by(Sort.Direction.fromString(filterDto.getSortDirection()), filterDto.getSortBy());
            Pageable pageable = PageRequest.of(filterDto.getPage(), filterDto.getSize(), sort);

            Page<Request> requestPage = requestRepository.findByUserWithFilters(
                    userPrincipal.getUserId(),
                    filterDto.getSearch(),
                    filterDto.getStatus(),
                    pageable
            );

            List<RequestResponseDto> requestDtos = requestPage.getContent().stream()
                    .map(this::convertToResponseDto)
                    .collect(Collectors.toList());

            Map<String, Object> extras = new HashMap<>();
            extras.put("totalElements", requestPage.getTotalElements());
            extras.put("totalPages", requestPage.getTotalPages());
            extras.put("currentPage", requestPage.getNumber());
            extras.put("pageSize", requestPage.getSize());

            return new GqlResponseDto<>(true, ResponseCode.SUCCESS, requestDtos, extras);

        } catch (Exception e) {
            log.error("Error fetching user requests: ", e);
            return new GqlResponseDto<>(false, ResponseCode.FAILURE, null, "Error fetching requests: " + e.getMessage());
        }
    }

    @Override
    public GqlResponseDto<RequestResponseDto> getAllRequests(RequestFilterDto filterDto) {
        try {
            log.info("Fetching all requests with filters: {}", filterDto);

            Sort sort = Sort.by(Sort.Direction.fromString(filterDto.getSortDirection()), filterDto.getSortBy());
            Pageable pageable = PageRequest.of(filterDto.getPage(), filterDto.getSize(), sort);

            Page<Request> requestPage = requestRepository.findAllWithFilters(
                    filterDto.getSearch(),
                    filterDto.getStatus(),
                    pageable
            );

            List<RequestResponseDto> requestDtos = requestPage.getContent().stream()
                    .map(this::convertToResponseDto)
                    .collect(Collectors.toList());

            Map<String, Object> extras = new HashMap<>();
            extras.put("totalElements", requestPage.getTotalElements());
            extras.put("totalPages", requestPage.getTotalPages());
            extras.put("currentPage", requestPage.getNumber());
            extras.put("pageSize", requestPage.getSize());

            return new GqlResponseDto<>(true, ResponseCode.SUCCESS, requestDtos, extras);

        } catch (Exception e) {
            log.error("Error fetching all requests: ", e);
            return new GqlResponseDto<>(false, ResponseCode.FAILURE, null, "Error fetching requests: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public GqlResponseDto<RequestResponseDto> approveRequest(RequestApprovalDto approvalDto, UserPrincipal userPrincipal) {
        try {
            log.info("Processing approval for request UUID: {} by user: {}", approvalDto.getRequestUuid(), userPrincipal.getUsername());

            // Validate decision
            if (approvalDto.getDecision() != Request.RequestStatus.APPROVED &&
                approvalDto.getDecision() != Request.RequestStatus.REJECTED) {
                return new GqlResponseDto<>(false, ResponseCode.INVALID_REQUEST, null, "Decision must be APPROVED or REJECTED");
            }

            Optional<Request> requestOpt = requestRepository.findFirstByUuid(UUID.fromString(approvalDto.getRequestUuid()));
            if (requestOpt.isEmpty()) {
                return new GqlResponseDto<>(false, ResponseCode.NO_RECORD_FOUND, null, "Request not found");
            }

            Request request = requestOpt.get();

            // Check if request is still pending
            if (request.getStatus() != Request.RequestStatus.PENDING) {
                return new GqlResponseDto<>(false, ResponseCode.CUSTOM, null, "Only pending requests can be approved or rejected");
            }

            // Get the approver user
            Optional<UserAccount> approverOpt = userAccountRepository.findById(userPrincipal.getUserId());
            if (approverOpt.isEmpty()) {
                return new GqlResponseDto<>(false, ResponseCode.UNAUTHORIZED, null, "Approver user not found");
            }

            request.setStatus(approvalDto.getDecision());
            request.setApprovedBy(approverOpt.get());
            request.setApprovedDate(LocalDateTime.now());
            request.setApprovalComment(approvalDto.getComment());
            request.setUpdatedAt(LocalDateTime.now());

            Request savedRequest = requestRepository.save(request);
            log.info("Request {} successfully with ID: {}", approvalDto.getDecision().name().toLowerCase(), savedRequest.getId());

            return new GqlResponseDto<>(true, ResponseCode.SUCCESS, convertToResponseDto(savedRequest));

        } catch (Exception e) {
            log.error("Error processing request approval: ", e);
            return new GqlResponseDto<>(false, ResponseCode.FAILURE, null, "Error processing approval: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public GqlResponseDto<String> deleteRequest(String uuid, UserPrincipal userPrincipal) {
        try {
            log.info("Deleting request with UUID: {} by user: {}", uuid, userPrincipal.getUsername());

            Optional<Request> requestOpt = requestRepository.findFirstByUuid(UUID.fromString(uuid));
            if (requestOpt.isEmpty()) {
                return new GqlResponseDto<>(false, ResponseCode.NO_RECORD_FOUND, null, "Request not found");
            }

            Request request = requestOpt.get();

            // Check if user owns the request
            if (!request.getRequestedBy().getId().equals(userPrincipal.getUserId())) {
                return new GqlResponseDto<>(false, ResponseCode.UNAUTHORIZED, null, "You can only delete your own requests");
            }

            // Check if request is still pending
            if (request.getStatus() != Request.RequestStatus.PENDING) {
                return new GqlResponseDto<>(false, ResponseCode.CUSTOM, null, "Only pending requests can be deleted");
            }

            requestRepository.delete(request);
            log.info("Request deleted successfully with UUID: {}", uuid);

            return new GqlResponseDto<>(true, ResponseCode.SUCCESS, "Request deleted successfully");

        } catch (Exception e) {
            log.error("Error deleting request: ", e);
            return new GqlResponseDto<>(false, ResponseCode.FAILURE, null, "Error deleting request: " + e.getMessage());
        }
    }

    /**
     * Convert Request entity to RequestResponseDto
     */
    private RequestResponseDto convertToResponseDto(Request request) {
        RequestResponseDto dto = new RequestResponseDto();
        dto.setUuid(request.getUuid().toString());
        dto.setTitle(request.getTitle());
        dto.setDescription(request.getDescription());
        dto.setStatus(request.getStatus());
        dto.setRequestedDate(request.getRequestedDate());
        dto.setApprovedDate(request.getApprovedDate());
        dto.setApprovalComment(request.getApprovalComment());
        dto.setCreatedAt(request.getCreatedAt());
        dto.setUpdatedAt(request.getUpdatedAt());

        // Set requested by user info
        if (request.getRequestedBy() != null) {
            RequestResponseDto.UserInfo requestedByInfo = new RequestResponseDto.UserInfo();
            requestedByInfo.setUuid(request.getRequestedBy().getUuid().toString());
            requestedByInfo.setUsername(request.getRequestedBy().getUsername());
            requestedByInfo.setFullName(request.getRequestedBy().getFullName());
            requestedByInfo.setEmail(request.getRequestedBy().getEmail());
            dto.setRequestedBy(requestedByInfo);
        }

        // Set approved by user info
        if (request.getApprovedBy() != null) {
            RequestResponseDto.UserInfo approvedByInfo = new RequestResponseDto.UserInfo();
            approvedByInfo.setUuid(request.getApprovedBy().getUuid().toString());
            approvedByInfo.setUsername(request.getApprovedBy().getUsername());
            approvedByInfo.setFullName(request.getApprovedBy().getFullName());
            approvedByInfo.setEmail(request.getApprovedBy().getEmail());
            dto.setApprovedBy(approvedByInfo);
        }

        return dto;
    }

    /**
     * Helper method to get Request by UUID (following RoleServiceImpl pattern)
     */
    private Optional<Request> getOptionalByUuid(String uuid) {
        return uuid != null && !uuid.isEmpty() ? requestRepository.findFirstByUuid(UUID.fromString(uuid)) : Optional.empty();
    }
}
