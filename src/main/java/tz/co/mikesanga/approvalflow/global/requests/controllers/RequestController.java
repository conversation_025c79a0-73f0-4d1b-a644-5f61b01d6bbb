package tz.co.mikesanga.approvalflow.global.requests.controllers;

import io.leangen.graphql.annotations.GraphQLArgument;
import io.leangen.graphql.annotations.GraphQLMutation;
import io.leangen.graphql.annotations.GraphQLQuery;
import io.leangen.graphql.spqr.spring.annotations.GraphQLApi;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import tz.co.mikesanga.approvalflow.global.dtos.GqlResponseDto;
import tz.co.mikesanga.approvalflow.global.requests.dtos.RequestApprovalDto;
import tz.co.mikesanga.approvalflow.global.requests.dtos.RequestCreateDto;
import tz.co.mikesanga.approvalflow.global.requests.dtos.RequestFilterDto;
import tz.co.mikesanga.approvalflow.global.requests.dtos.RequestResponseDto;
import tz.co.mikesanga.approvalflow.security.UserPrincipal;
import tz.co.mikesanga.approvalflow.global.requests.services.RequestService;

@GraphQLApi
@Service
@RequiredArgsConstructor
public class RequestController {

    private final RequestService requestService;

    @GraphQLMutation(name = "saveRequest", description = "Save a request (create new if uuid is null, update existing if uuid is provided)")
    @PreAuthorize("hasAnyRole('ROLE_REQUESTS_CREATE', 'ROLE_REQUESTS_EDIT')")
    public GqlResponseDto<RequestResponseDto> saveRequest(
            @GraphQLArgument(name = "requestDto") RequestCreateDto requestDto) {
        UserPrincipal userPrincipal = getCurrentUser();
        return requestService.saveRequest(requestDto, userPrincipal);
    }

    @GraphQLQuery(name = "getRequestByUuid", description = "Get a request by UUID")
    @PreAuthorize("hasRole('ROLE_REQUESTS_VIEW')")
    public GqlResponseDto<RequestResponseDto> getRequestByUuid(
            @GraphQLArgument(name = "uuid") String uuid) {
        UserPrincipal userPrincipal = getCurrentUser();
        return requestService.getRequestByUuid(uuid, userPrincipal);
    }

    @GraphQLQuery(name = "getMyRequests", description = "Get requests for the current user")
    @PreAuthorize("hasRole('ROLE_REQUESTS_VIEW')")
    public GqlResponseDto<RequestResponseDto> getMyRequests(
            @GraphQLArgument(name = "filter") RequestFilterDto filterDto) {
        if (filterDto == null) {
            filterDto = new RequestFilterDto();
        }
        UserPrincipal userPrincipal = getCurrentUser();
        return requestService.getMyRequests(filterDto, userPrincipal);
    }

    @GraphQLQuery(name = "getAllRequests", description = "Get all requests (admin only)")
    @PreAuthorize("hasRole('ROLE_REQUESTS_VIEW_ALL')")
    public GqlResponseDto<RequestResponseDto> getAllRequests(
            @GraphQLArgument(name = "filter") RequestFilterDto filterDto) {
        if (filterDto == null) {
            filterDto = new RequestFilterDto();
        }
        return requestService.getAllRequests(filterDto);
    }

    @GraphQLMutation(name = "approveRequest", description = "Approve or reject a request (admin only)")
    @PreAuthorize("hasRole('ROLE_REQUESTS_APPROVE')")
    public GqlResponseDto<RequestResponseDto> approveRequest(
            @GraphQLArgument(name = "approvalDto") RequestApprovalDto approvalDto) {
        UserPrincipal userPrincipal = getCurrentUser();
        return requestService.approveRequest(approvalDto, userPrincipal);
    }

    @GraphQLMutation(name = "deleteRequest", description = "Delete a request (only pending requests by owner)")
    @PreAuthorize("hasRole('ROLE_REQUESTS_DELETE')")
    public GqlResponseDto<String> deleteRequest(
            @GraphQLArgument(name = "uuid") String uuid) {
        UserPrincipal userPrincipal = getCurrentUser();
        return requestService.deleteRequest(uuid, userPrincipal);
    }

    /**
     * Helper method to get the current authenticated user
     */
    private UserPrincipal getCurrentUser() {
        return (UserPrincipal) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
    }
}
