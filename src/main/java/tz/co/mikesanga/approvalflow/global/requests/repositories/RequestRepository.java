package tz.co.mikesanga.approvalflow.global.requests.repositories;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.datatables.repository.DataTablesRepository;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import tz.co.mikesanga.approvalflow.global.requests.entities.Request;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface RequestRepository extends JpaRepository<Request, Long>, DataTablesRepository<Request, Long> {
    
    Optional<Request> findFirstByUuid(UUID uuid);
    
    // Find requests by the user who created them
    @Query("SELECT r FROM Request r WHERE r.requestedBy.id = :userId")
    Page<Request> findByRequestedByUserId(@Param("userId") Long userId, Pageable pageable);
    
    // Find requests by the user who created them with status filter
    @Query("SELECT r FROM Request r WHERE r.requestedBy.id = :userId AND " +
           "(:status IS NULL OR r.status = :status)")
    Page<Request> findByRequestedByUserIdAndStatus(@Param("userId") Long userId, 
                                                   @Param("status") Request.RequestStatus status, 
                                                   Pageable pageable);
    
    // Find all requests with filters (for admin users) - simplified approach
    @Query("SELECT r FROM Request r WHERE " +
           "(:search IS NULL OR :search = '' OR " +
           "UPPER(r.title) LIKE UPPER(CONCAT('%', :search, '%')) OR " +
           "UPPER(r.description) LIKE UPPER(CONCAT('%', :search, '%')) OR " +
           "UPPER(r.requestedBy.fullName) LIKE UPPER(CONCAT('%', :search, '%'))) AND " +
           "(:status IS NULL OR r.status = :status)")
    Page<Request> findAllWithFilters(@Param("search") String search,
                                    @Param("status") Request.RequestStatus status,
                                    Pageable pageable);

    // Find requests by user with search filter - simplified approach
    @Query("SELECT r FROM Request r WHERE r.requestedBy.id = :userId AND " +
           "(:search IS NULL OR :search = '' OR " +
           "UPPER(r.title) LIKE UPPER(CONCAT('%', :search, '%')) OR " +
           "UPPER(r.description) LIKE UPPER(CONCAT('%', :search, '%'))) AND " +
           "(:status IS NULL OR r.status = :status)")
    Page<Request> findByUserWithFilters(@Param("userId") Long userId,
                                       @Param("search") String search,
                                       @Param("status") Request.RequestStatus status,
                                       Pageable pageable);
    
    // Count requests by status
    @Query("SELECT COUNT(r) FROM Request r WHERE r.status = :status")
    long countByStatus(@Param("status") Request.RequestStatus status);
    
    // Count requests by user and status
    @Query("SELECT COUNT(r) FROM Request r WHERE r.requestedBy.id = :userId AND r.status = :status")
    long countByUserAndStatus(@Param("userId") Long userId, @Param("status") Request.RequestStatus status);
    
    // Find pending requests for approval
    @Query("SELECT r FROM Request r WHERE r.status = 'PENDING' ORDER BY r.requestedDate ASC")
    Page<Request> findPendingRequests(Pageable pageable);
    
    // Check if user owns the request
    @Query("SELECT COUNT(r) > 0 FROM Request r WHERE r.uuid = :uuid AND r.requestedBy.id = :userId")
    boolean isRequestOwnedByUser(@Param("uuid") UUID uuid, @Param("userId") Long userId);
}
