package tz.co.mikesanga.approvalflow.global.requests.services;

import tz.co.mikesanga.approvalflow.global.dtos.GqlResponseDto;
import tz.co.mikesanga.approvalflow.global.requests.dtos.RequestApprovalDto;
import tz.co.mikesanga.approvalflow.global.requests.dtos.RequestCreateDto;
import tz.co.mikesanga.approvalflow.global.requests.dtos.RequestFilterDto;
import tz.co.mikesanga.approvalflow.global.requests.dtos.RequestResponseDto;
import tz.co.mikesanga.approvalflow.security.UserPrincipal;

public interface RequestService {

    /**
     * Save a request (create new if uuid is null, update existing if uuid is provided)
     */
    GqlResponseDto<RequestResponseDto> saveRequest(RequestCreateDto requestDto, UserPrincipal userPrincipal);

    /**
     * Get a request by UUID
     */
    GqlResponseDto<RequestResponseDto> getRequestByUuid(String uuid, UserPrincipal userPrincipal);

    /**
     * Get requests for the current user
     */
    GqlResponseDto<RequestResponseDto> getMyRequests(RequestFilterDto filterDto, UserPrincipal userPrincipal);

    /**
     * Get all requests (admin only)
     */
    GqlResponseDto<RequestResponseDto> getAllRequests(RequestFilterDto filterDto);

    /**
     * Approve or reject a request (admin only)
     */
    GqlResponseDto<RequestResponseDto> approveRequest(RequestApprovalDto approvalDto, UserPrincipal userPrincipal);

    /**
     * Delete a request (only by owner and only if PENDING)
     */
    GqlResponseDto<String> deleteRequest(String uuid, UserPrincipal userPrincipal);
}
