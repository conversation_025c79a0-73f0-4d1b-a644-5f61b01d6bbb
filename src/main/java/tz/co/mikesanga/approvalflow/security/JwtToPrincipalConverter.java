package tz.co.mikesanga.approvalflow.security;


import com.auth0.jwt.interfaces.DecodedJWT;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.stereotype.Component;
import tz.co.mikesanga.approvalflow.uaa.services.PermissionService;

import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor
public class JwtToPrincipalConverter {

    private final PermissionService permissionService;

    public UserPrincipal convert(DecodedJWT decodedJWT) {

        return UserPrincipal.builder()
                .userId(Long.valueOf(decodedJWT.getSubject()))
                .username(decodedJWT.getClaim("username").asString())
                .authorities(extractAuthoritiesFromClam(decodedJWT))
                .build();
    }

    public List<SimpleGrantedAuthority> extractAuthoritiesFromClam(DecodedJWT decodedJWT) {
        var claim = decodedJWT.getClaim("roles");
        if (claim == null || claim.isMissing()) return List.of();
        List<String> permissionNames = permissionService.getPermissionsByRoleNames(claim.asList(String.class));
        return permissionNames.stream().map(SimpleGrantedAuthority::new).toList();
    }
}
