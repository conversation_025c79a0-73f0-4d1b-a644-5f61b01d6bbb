package tz.co.mikesanga.approvalflow.security;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class JwtDecoder {

    private final JwtProperties jwtProperties;

    public DecodedJWT decode(String token) {

        var decoded = JWT.require(Algorithm.HMAC256(jwtProperties.getSecretKey()))
                .build()
                .verify(token);
        log.info("JWT token decoded: {}", decoded);
        return decoded;
    }
}
