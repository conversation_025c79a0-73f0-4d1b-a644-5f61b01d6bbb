package tz.co.mikesanga.approvalflow.security;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import tz.co.mikesanga.approvalflow.uaa.entities.UserAccount;
import tz.co.mikesanga.approvalflow.uaa.services.PermissionService;
import tz.co.mikesanga.approvalflow.uaa.services.UserAccountService;

import java.util.ArrayList;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class AuthUserDetailsService implements UserDetailsService {


    private final UserAccountService userAccountService;
    private final PermissionService permissionService;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {

        Optional<UserAccount> optionalUserByEmail = userAccountService.getOptionalByEmail(username);
        Optional<UserAccount> optionalUserByUsername = userAccountService.getOptionalByUsername(username);
        UserAccount userAccount = optionalUserByEmail.orElse(
                optionalUserByUsername.orElseThrow(
                        () -> {
                            log.error("username was not found");
                            return  new UsernameNotFoundException(username);
                        }
                )
        );

        return UserPrincipal.builder()
                .userId(userAccount.getId())
                .username(userAccount.getUsername())
                .password(userAccount.getPassword())
                .authorities(new ArrayList<>())
                .build();
    }
}
