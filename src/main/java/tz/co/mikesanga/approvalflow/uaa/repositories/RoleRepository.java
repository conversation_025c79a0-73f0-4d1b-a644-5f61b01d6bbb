package tz.co.mikesanga.approvalflow.uaa.repositories;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import tz.co.mikesanga.approvalflow.uaa.entities.Role;

import java.util.List;
import java.util.Optional;
import java.util.UUID;


@Repository
public interface RoleRepository extends JpaRepository<Role, Long> {
    Optional<Role> findFirstByName(String name);

    List<Role> findAllByOrderByName();

    Optional<Role> findFirstByUuid(UUID uuid);

    Optional<Role> findFirstByNameIgnoreCase(String name);

    Optional<Role> findFirstByDisplayNameIgnoreCase(String name);
}
