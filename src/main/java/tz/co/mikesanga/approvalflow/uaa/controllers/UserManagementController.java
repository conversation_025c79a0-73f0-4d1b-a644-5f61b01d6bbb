package tz.co.mikesanga.approvalflow.uaa.controllers;

import io.leangen.graphql.annotations.GraphQLArgument;
import io.leangen.graphql.annotations.GraphQLMutation;
import io.leangen.graphql.annotations.GraphQLQuery;
import io.leangen.graphql.spqr.spring.annotations.GraphQLApi;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import tz.co.mikesanga.approvalflow.global.dtos.GqlResponseDto;
import tz.co.mikesanga.approvalflow.uaa.dtos.*;
import tz.co.mikesanga.approvalflow.uaa.entities.UserAccount;
import tz.co.mikesanga.approvalflow.uaa.services.UserManagementService;

@GraphQLApi
@Service
@RequiredArgsConstructor
public class UserManagementController {

    private final UserManagementService userManagementService;

    @GraphQLQuery(name = "listUsers", description = "Get all users with pagination and filtering")
    @PreAuthorize("hasRole('ROLE_USERS_VIEW')")
    public GqlResponseDto<UserResponseDto> listUsers(@GraphQLArgument(name = "filter") UserFilterDto filterDto) {
        if (filterDto == null) {
            filterDto = new UserFilterDto();
        }
        return userManagementService.getAllUsers(filterDto);
    }

    @GraphQLQuery(name = "getUserByUuid", description = "Get user by UUID")
    @PreAuthorize("hasRole('ROLE_USERS_VIEW')")
    public GqlResponseDto<UserResponseDto> getUserByUuid(@GraphQLArgument(name = "uuid") String uuid) {
        return userManagementService.getUserByUuid(uuid);
    }

    @GraphQLMutation(name = "saveUser", description = "Save a user (create new if uuid is null, update existing if uuid is provided)")
    @PreAuthorize("hasAnyRole('ROLE_USERS_ADD', 'ROLE_USERS_EDIT')")
    public GqlResponseDto<UserResponseDto> saveUser(@GraphQLArgument(name = "userDto") UserCreateRequestDto userDto) {
        return userManagementService.saveUser(userDto);
    }

    @GraphQLMutation(name = "deactivateUser", description = "Deactivate a user")
    @PreAuthorize("hasRole('ROLE_USERS_DEACTIVATE')")
    public GqlResponseDto<UserResponseDto> deactivateUser(@GraphQLArgument(name = "uuid") String uuid) {
        return userManagementService.deactivateUser(uuid);
    }

    @GraphQLMutation(name = "reactivateUser", description = "Reactivate a user")
    @PreAuthorize("hasRole('ROLE_USERS_DEACTIVATE')")
    public GqlResponseDto<UserResponseDto> reactivateUser(@GraphQLArgument(name = "uuid") String uuid) {
        return userManagementService.reactivateUser(uuid);
    }



    @GraphQLMutation(name = "changeUserStatus", description = "Change user status")
    @PreAuthorize("hasRole('ROLE_USERS_EDIT')")
    public GqlResponseDto<UserAccount> changeUserStatus(@GraphQLArgument(name = "uuid") String uuid, 
                                                       @GraphQLArgument(name = "status") UserAccount.UserStatus status) {
        return userManagementService.changeUserStatus(uuid, status);
    }
}
