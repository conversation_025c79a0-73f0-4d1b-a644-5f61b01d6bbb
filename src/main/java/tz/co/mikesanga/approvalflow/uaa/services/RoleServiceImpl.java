package tz.co.mikesanga.approvalflow.uaa.services;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tz.co.mikesanga.approvalflow.global.dtos.GqlResponseDto;
import tz.co.mikesanga.approvalflow.global.dtos.ResponseCode;
import tz.co.mikesanga.approvalflow.uaa.dtos.RoleDto;
import tz.co.mikesanga.approvalflow.uaa.entities.Permission;
import tz.co.mikesanga.approvalflow.uaa.entities.Role;
import tz.co.mikesanga.approvalflow.uaa.entities.UserAccount;
import tz.co.mikesanga.approvalflow.uaa.repositories.PermissionRepository;
import tz.co.mikesanga.approvalflow.uaa.repositories.RoleRepository;
import tz.co.mikesanga.approvalflow.uaa.repositories.UserAccountRepository;
import tz.co.mikesanga.approvalflow.utils.Constants;
import tz.co.mikesanga.approvalflow.utils.Utils;

import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class RoleServiceImpl implements RoleService, TableSeeder {

    private final RoleRepository roleRepository;
    private final UserAccountService userAccountService;
    private final PermissionRepository permissionRepository;
    private final UserAccountRepository userAccountRepository;


    @Override
    public void seed() {
        log.info("Seeding roles...");
        Optional<UserAccount> optionalUser = userAccountService.getOptionalByUsername(Constants.SuperAdmin.USERNAME);
        if (optionalUser.isEmpty()) {
            return;
        }
        List<Role> roles = new ArrayList<>() {
            {
                add(new Role(Utils.toSlugUnderScore(Constants.DefaultRole.NAME), Constants.DefaultRole.NAME, Constants.DefaultRole.DESCRIPTION, Collections.emptyList()));
                add(new Role(Utils.toSlugUnderScore(Constants.SystemUserRole.NAME), Constants.SystemUserRole.NAME, Constants.SystemUserRole.DESCRIPTION, Collections.emptyList()));
            }
        };

        for (Role role : roles) {
            Optional<Role> existingRole = roleRepository.findFirstByName(role.getName());
            Role newRole = existingRole.orElse(new Role());
            newRole.setName(role.getName());
            newRole.setDisplayName(role.getDisplayName());
            newRole.setDescription(role.getDescription());
            if (existingRole.isEmpty()) {
                newRole.setPermissions(role.getPermissions());
            }
            roleRepository.save(newRole);
        }
        assignPermissionsToAdminRole();
        assignPermissionsToSystemUserRole();
        assignAdminRoleToSuperAdmin();
    }

    private void assignPermissionsToAdminRole() {
        log.info("Assigning all permissions to super admin role...");
        List<Permission> allPermissions = permissionRepository.findAll();
        Optional<Role> developerRole = roleRepository.findFirstByName(Constants.DefaultRole.NAME);
        developerRole.ifPresent(role -> {
            role.setPermissions(allPermissions);
            roleRepository.save(role);
        });
    }

    private void assignPermissionsToSystemUserRole() {
        log.info("Assigning basic permissions to system user role...");
        List<Permission> permissions = new ArrayList<>();

        // Add ROLE_DASHBOARD_VIEW permission
        Optional<Permission> dashboardPermission = permissionRepository.findPermissionByName("ROLE_DASHBOARD_VIEW");
        dashboardPermission.ifPresent(permissions::add);

        // Add ROLE_PROFILE_VIEW permission
        Optional<Permission> profilePermission = permissionRepository.findPermissionByName("ROLE_PROFILE_VIEW");
        profilePermission.ifPresent(permissions::add);

        Optional<Role> systemUserRole = roleRepository.findFirstByName(Constants.SystemUserRole.NAME);
        systemUserRole.ifPresent(role -> {
            role.setPermissions(permissions);
            roleRepository.save(role);
        });
    }

    private void assignAdminRoleToSuperAdmin() {
        log.info("Assigning all admin role...");
        Optional<Role> optionalRole = roleRepository.findFirstByName(Constants.DefaultRole.NAME);
        Optional<UserAccount> optionalUser = userAccountService.getOptionalByUsername(Constants.SuperAdmin.USERNAME);
        if (optionalRole.isEmpty() || optionalUser.isEmpty()) {
            return;
        }
        UserAccount userAccount = optionalUser.get();
        userAccount.setRoles(List.of(optionalRole.get()));
        userAccountRepository.save(userAccount);
    }


    @Override
    public List<String> getRoleStringsByUsername(String username) {
        return username != null && !username.isEmpty() ? userAccountRepository.getRoleByUsername(username) : List.of();
    }

    @Override
    public GqlResponseDto<Role> getAll() {
        return new GqlResponseDto<>(true, ResponseCode.SUCCESS, roleRepository.findAllByOrderByName());
    }

    @Override
    public GqlResponseDto<Role> save(RoleDto roleDto) {
        Optional<Role> optionalRole = getOptionalByUid(roleDto.getUuid());
        if (roleDto.getUuid() != null && optionalRole.isEmpty()) {
            return new GqlResponseDto<>(false, ResponseCode.CUSTOM, null, "Role edit failed: The role was not found or may have been removed.");
        }

        Role role = optionalRole.orElse(new Role());
        if (validateNameExists(roleDto.getDisplayName(), role.getId())) {
            return new GqlResponseDto<>(false, ResponseCode.CUSTOM, null, "Role already exists.");
        }

        if(roleDto.getPermissionUuids() == null || roleDto.getPermissionUuids().isEmpty()) {
            return new GqlResponseDto<>(false, ResponseCode.CUSTOM, null, "No permissions were provided.");
        }

        List<Permission> permissions = permissionRepository.findAllByUuidIn(roleDto.getPermissionUuids().stream().map(UUID::fromString).toList());


        role.setName(Utils.toSlugUnderScore(roleDto.getDisplayName().trim()));
        role.setDisplayName(roleDto.getDisplayName());
        role.setPermissions(permissions);
        return new GqlResponseDto<>(true, ResponseCode.SUCCESS, roleRepository.save(role));
    }

    private boolean validateNameExists(String name, Long onUpdate) {
        Optional<Role> optionalRole = roleRepository.findFirstByDisplayNameIgnoreCase(name.trim());

        if (onUpdate != null) {
            return optionalRole.isPresent() && !optionalRole.get().getId().equals(onUpdate);
        }
        return optionalRole.isPresent();
    }

    public Optional<Role> getOptionalByUid(String uid) {
        return uid != null && !uid.isEmpty() ? roleRepository.findFirstByUuid(UUID.fromString(uid)) : Optional.empty();
    }
}
