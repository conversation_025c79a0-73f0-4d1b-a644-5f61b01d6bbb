package tz.co.mikesanga.approvalflow.uaa.services;

import tz.co.mikesanga.approvalflow.global.dtos.GqlResponseDto;
import tz.co.mikesanga.approvalflow.uaa.dtos.PermissionResponseDto;
import tz.co.mikesanga.approvalflow.uaa.entities.Permission;
import tz.co.mikesanga.approvalflow.uaa.entities.Role;

import java.util.List;

public interface PermissionService {
    void seed();

    List<String> getPermissionsByRoleNames(List<String> list);

    GqlResponseDto<PermissionResponseDto> getAllPermissions();

    List<Permission> getByRole(Role role);
}
