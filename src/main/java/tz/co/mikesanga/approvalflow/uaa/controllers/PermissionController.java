package tz.co.mikesanga.approvalflow.uaa.controllers;

import io.leangen.graphql.annotations.GraphQLContext;
import io.leangen.graphql.annotations.GraphQLQuery;
import io.leangen.graphql.spqr.spring.annotations.GraphQLApi;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import tz.co.mikesanga.approvalflow.global.dtos.GqlResponseDto;
import tz.co.mikesanga.approvalflow.uaa.dtos.PermissionResponseDto;
import tz.co.mikesanga.approvalflow.uaa.entities.Permission;
import tz.co.mikesanga.approvalflow.uaa.entities.Role;
import tz.co.mikesanga.approvalflow.uaa.services.PermissionService;

import java.util.List;

@GraphQLApi
@Service
@RequiredArgsConstructor
@Slf4j
public class PermissionController {

    private final PermissionService permissionService;

    @GraphQLQuery(name = "listPermissions", description = "Get all permissions")
    @PreAuthorize("hasRole('ROLE_PERMISSIONS_VIEW')")
    public GqlResponseDto<PermissionResponseDto> listPermissions() {
        log.info("Listing all permissions");
        return permissionService.getAllPermissions();
    }


    @GraphQLQuery
    public List<Permission> rolePermissions(@GraphQLContext Role role) {
        return permissionService.getByRole(role);
    }
}
