package tz.co.mikesanga.approvalflow.uaa.controllers;

import io.leangen.graphql.annotations.GraphQLArgument;
import io.leangen.graphql.annotations.GraphQLContext;
import io.leangen.graphql.annotations.GraphQLMutation;
import io.leangen.graphql.annotations.GraphQLQuery;
import io.leangen.graphql.spqr.spring.annotations.GraphQLApi;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import tz.co.mikesanga.approvalflow.global.dtos.GqlResponseDto;
import tz.co.mikesanga.approvalflow.uaa.dtos.RoleDto;
import tz.co.mikesanga.approvalflow.uaa.entities.Permission;
import tz.co.mikesanga.approvalflow.uaa.entities.Role;
import tz.co.mikesanga.approvalflow.uaa.services.PermissionService;
import tz.co.mikesanga.approvalflow.uaa.services.RoleService;

import java.util.List;

@GraphQLApi
@Service
@RequiredArgsConstructor
public class RolesController {

    private final RoleService roleService;


    @GraphQLQuery(name = "listRoles")
    @PreAuthorize("hasAnyRole('ROLE_ROLES_VIEW')")
    public GqlResponseDto<Role> listCompanyRoles() {
        return roleService.getAll();
    }


    @GraphQLMutation(name = "saveRole")
    @PreAuthorize("hasAnyRole('ROLE_ROLES_ADD', 'ROLE_ROLES_EDIT')")
    public GqlResponseDto<Role> saveCompanyRole(@GraphQLArgument(name = "roleDto") RoleDto roleDto) {
        return roleService.save(roleDto);
    }




    /*@GraphQLQuery(name = "getCompanyRoleById", description = "Returns company role by id")
    @PreAuthorize("hasAnyRole('ROLE_COMPANY_ROLE_VIEW','ROLE_COMPANY_ROLE_ASSIGN_PERMISSIONS','ROLE_COMPANY_ROLE_VIEW')")
    public GqlResponseDto<Role> getCompanyRoleById(@GraphQLArgument(name = "roleId") Long roleId) {
        return roleService.getCompanyById(roleId);
    }


    @GraphQLMutation(name = "deleteCompanyRoleById", description = "Deletes company role by id")
    @PreAuthorize("hasRole('ROLE_COMPANY_ROLE_DELETE')")
    public GqlResponseDto<Role> deleteCompanyRole(@GraphQLArgument(name = "roleId") Long roleId) {
        return roleService.deleteById(roleId);
    }

    @GraphQLMutation(name = "assignPermissionsToRole", description = "")
    @PreAuthorize("hasRole('ROLE_COMPANY_ROLE_ASSIGN_PERMISSIONS')")
    public GqlResponseDto<Role> assignPermissionsToRole(@GraphQLArgument(name = "role") Role role, @GraphQLArgument(name = "permissions") List<Permission> permissions) {
        return roleService.assignPermissionsToRole(role, permissions);
    }*/
}
