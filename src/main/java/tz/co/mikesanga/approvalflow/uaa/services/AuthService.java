package tz.co.mikesanga.approvalflow.uaa.services;

import org.springframework.http.ResponseEntity;
import tz.co.mikesanga.approvalflow.security.UserPrincipal;
import tz.co.mikesanga.approvalflow.uaa.dtos.LoginRequest;
import tz.co.mikesanga.approvalflow.uaa.dtos.LoginResponse;
import tz.co.mikesanga.approvalflow.uaa.dtos.MeDto;

public interface AuthService {
    ResponseEntity<LoginResponse> attemptLogin(LoginRequest loginRequest);

    ResponseEntity<MeDto> getMe(UserPrincipal userPrincipal);
}
