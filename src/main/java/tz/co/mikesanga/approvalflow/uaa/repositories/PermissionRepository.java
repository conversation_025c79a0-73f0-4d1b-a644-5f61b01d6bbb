package tz.co.mikesanga.approvalflow.uaa.repositories;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import tz.co.mikesanga.approvalflow.uaa.entities.Permission;
import tz.co.mikesanga.approvalflow.uaa.entities.Role;

import java.util.List;
import java.util.Optional;
import java.util.UUID;


@Repository
public interface PermissionRepository extends JpaRepository<Permission, Long> {

    Optional<Permission> findPermissionByName(String name);

    @Modifying
    @Transactional
    @Query(value = "delete from permission_role where permission_id not in (:ids)", nativeQuery = true)
    void deleteRolePermissionByPermissionIdNotIn(@Param("ids") List<Long> ids);



    @Modifying
    @Transactional
    void deleteAllByIdNotIn(List<Long> ids);


    @Query(value = "select p.name from Permission  p where exists (select 1 from Role r join  r.permissions rp on rp = p and r.name in :list)")
    List<String> getPermissionsByRoleNames(List<String> list);

    @Query(value = "select p from Permission  p where exists (select 1 from Role r join r.permissions rp on rp = p and r = :role)")
    List<Permission> findAllByRole(Role role);

    List<Permission> findAllByUuidIn(List<UUID> permissionUuids);

}
