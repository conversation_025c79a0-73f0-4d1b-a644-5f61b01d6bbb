package tz.co.mikesanga.approvalflow.uaa.services;


import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import tz.co.mikesanga.approvalflow.security.JwtIssuer;
import tz.co.mikesanga.approvalflow.security.UserPrincipal;
import tz.co.mikesanga.approvalflow.uaa.dtos.LoginRequest;
import tz.co.mikesanga.approvalflow.uaa.dtos.LoginResponse;
import tz.co.mikesanga.approvalflow.uaa.dtos.MeDto;
import tz.co.mikesanga.approvalflow.uaa.entities.UserAccount;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements AuthService {

    private final JwtIssuer jwtIssuer;
    private final AuthenticationManager authenticationManager;
    private final RoleService roleService;
    private final UserAccountService userAccountService;

    @Override
    public ResponseEntity<LoginResponse> attemptLogin(LoginRequest loginRequest) {
        try {
            // Attempt authentication
            var authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(loginRequest.getUsername(), loginRequest.getPassword())
            );

            // Set authentication in the security context
            SecurityContextHolder.getContext().setAuthentication(authentication);
            var principal = (UserPrincipal) authentication.getPrincipal();

            // Generate token and return success response
            List<String> roleNames = roleService.getRoleStringsByUsername(principal.getUsername());
            var token = jwtIssuer.issuer(principal.getUserId(), principal.getUsername(), roleNames);
            return ResponseEntity.ok(new LoginResponse(token, "Successful", false, HttpStatus.OK.value()));

        } catch (BadCredentialsException ex) {
            // Handle invalid username or password
            return createErrorResponse(ex.getMessage(), HttpStatus.UNAUTHORIZED);

        } catch (AuthenticationException ex) {
            // Handle general authentication exceptions
            return createErrorResponse(ex.getMessage(), HttpStatus.UNAUTHORIZED);

        } catch (Exception ex) {
            // Handle unexpected errors
            return createErrorResponse("An unexpected error occurred", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public ResponseEntity<MeDto> getMe(UserPrincipal userPrincipal) {
        Optional<UserAccount> optionalUserAccount = userAccountService.getOptionalByUsername(userPrincipal.getUsername());
        if(optionalUserAccount.isEmpty()){
            return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED);
        }
        UserAccount userAccount = optionalUserAccount.get();
        return ResponseEntity.ok(new MeDto(
                userAccount.getUuid().toString(),
                userAccount.getEmail(),
                userAccount.getPhone(),
                userAccount.getFullName(),
                userPrincipal.getUsername(),
                userAccount.getPhoneCode(),
                userAccount.getStatus().toString(),
                userAccount.getRoles(),
                userPrincipal.getAuthorities().stream().map(GrantedAuthority::getAuthority).toList()
        ));
    }

    private ResponseEntity<LoginResponse> createErrorResponse(String message, HttpStatus status) {
        // Build error response body
        return ResponseEntity.status(status).body(LoginResponse.builder()
                .error(true)
                .message(message)
                .status(status.value())
                .build());
    }
}
