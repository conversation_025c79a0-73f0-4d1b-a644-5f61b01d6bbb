package tz.co.mikesanga.approvalflow.uaa.services;



import tz.co.mikesanga.approvalflow.global.dtos.GqlResponseDto;
import tz.co.mikesanga.approvalflow.uaa.dtos.RoleDto;
import tz.co.mikesanga.approvalflow.uaa.entities.Role;

import java.util.List;

public interface RoleService {
    void seed();

    List<String> getRoleStringsByUsername(String username);

    GqlResponseDto<Role> getAll();

    GqlResponseDto<Role> save(RoleDto roleDto);
}
