package tz.co.mikesanga.approvalflow.utils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;
import tz.co.mikesanga.approvalflow.uaa.services.PermissionService;
import tz.co.mikesanga.approvalflow.uaa.services.RoleService;
import tz.co.mikesanga.approvalflow.uaa.services.UserAccountService;


@Component
@Slf4j
@RequiredArgsConstructor
public class Initializer implements ApplicationRunner {


    private final UserAccountService userAccountService;
    private final PermissionService permissionService;
    private final RoleService roleService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        seeder();
    }


    private void seeder() {
        userAccountService.seed();
        permissionService.seed();
        roleService.seed();
    }
}
