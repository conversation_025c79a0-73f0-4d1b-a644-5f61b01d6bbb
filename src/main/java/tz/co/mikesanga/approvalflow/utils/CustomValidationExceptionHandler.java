package tz.co.mikesanga.approvalflow.utils;

import graphql.ErrorClassification;
import graphql.ErrorType;
import graphql.GraphQL;
import graphql.GraphQLError;
import graphql.execution.*;
import graphql.language.SourceLocation;
import graphql.schema.GraphQLSchema;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Component
@Getter
@Setter
public class CustomValidationExceptionHandler {
    @Bean
    public GraphQL graphQL(GraphQLSchema schema) {
        return GraphQL.newGraphQL(schema)
                .queryExecutionStrategy(new AsyncExecutionStrategy(new CustomDataFetcherExceptionHandler()))
                .mutationExecutionStrategy(new AsyncSerialExecutionStrategy(new CustomDataFetcherExceptionHandler()))
                .build();
    }

    private static class CustomDataFetcherExceptionHandler implements DataFetcherExceptionHandler {


        @Override
        public CompletableFuture<DataFetcherExceptionHandlerResult> handleException(DataFetcherExceptionHandlerParameters handlerParameters) {
            return null;
        }
    }

    private static class CustomExceptionWhileDataFetching implements GraphQLError {

        private final String message;
        private final List<SourceLocation> locations;

        public CustomExceptionWhileDataFetching(Throwable exception, SourceLocation sourceLocation) {
            this.locations = Collections.singletonList(sourceLocation);
            this.message = exception.getMessage();
        }

        @Override
        public String getMessage() {
            return this.message;
        }

        @Override
        public List<SourceLocation> getLocations() {
            return this.locations;
        }

        @Override
        public ErrorClassification getErrorType() {
            return ErrorType.DataFetchingException;
        }

    }
}
