package tz.co.mikesanga.approvalflow.uaa.services;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import tz.co.mikesanga.approvalflow.global.dtos.GqlResponseDto;
import tz.co.mikesanga.approvalflow.global.dtos.ResponseCode;
import tz.co.mikesanga.approvalflow.uaa.dtos.PermissionResponseDto;
import tz.co.mikesanga.approvalflow.uaa.entities.Permission;
import tz.co.mikesanga.approvalflow.uaa.repositories.PermissionRepository;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PermissionServiceImplTest {

    @Mock
    private PermissionRepository permissionRepository;

    @InjectMocks
    private PermissionServiceImpl permissionService;

    @Test
    void getAllPermissions_Success() {
        // Given
        Permission permission1 = new Permission();
        permission1.setUuid(UUID.randomUUID().toString());
        permission1.setName("ROLE_USERS_VIEW");
        permission1.setDescription("Can view users");
        permission1.setGroupName("UAA");

        Permission permission2 = new Permission();
        permission2.setUuid(UUID.randomUUID().toString());
        permission2.setName("ROLE_PERMISSIONS_VIEW");
        permission2.setDescription("Can view permissions");
        permission2.setGroupName("UAA");

        List<Permission> permissions = Arrays.asList(permission1, permission2);
        when(permissionRepository.findAll()).thenReturn(permissions);

        // When
        GqlResponseDto<PermissionResponseDto> result = permissionService.getAllPermissions();

        // Then
        assertTrue(result.getStatus());
        assertEquals(ResponseCode.SUCCESS, result.getCode());
        assertNotNull(result.getDataList());
        assertEquals(2, result.getDataList().size());
        
        PermissionResponseDto dto1 = result.getDataList().get(0);
        assertEquals(permission1.getUuid(), dto1.getUuid());
        assertEquals(permission1.getName(), dto1.getName());
        assertEquals(permission1.getDescription(), dto1.getDescription());
        assertEquals(permission1.getGroupName(), dto1.getGroupName());
        
        verify(permissionRepository, times(1)).findAll();
    }

    @Test
    void getAllPermissions_EmptyList_Success() {
        // Given
        when(permissionRepository.findAll()).thenReturn(Arrays.asList());

        // When
        GqlResponseDto<PermissionResponseDto> result = permissionService.getAllPermissions();

        // Then
        assertTrue(result.getStatus());
        assertEquals(ResponseCode.SUCCESS, result.getCode());
        assertNotNull(result.getDataList());
        assertTrue(result.getDataList().isEmpty());
        
        verify(permissionRepository, times(1)).findAll();
    }

    @Test
    void getAllPermissions_RepositoryException_ReturnsError() {
        // Given
        when(permissionRepository.findAll()).thenThrow(new RuntimeException("Database error"));

        // When
        GqlResponseDto<PermissionResponseDto> result = permissionService.getAllPermissions();

        // Then
        assertFalse(result.getStatus());
        assertEquals(ResponseCode.CUSTOM, result.getCode());
        assertNull(result.getData());
        assertEquals("Failed to retrieve permissions", result.getErrorDescription());
        
        verify(permissionRepository, times(1)).findAll();
    }
}
