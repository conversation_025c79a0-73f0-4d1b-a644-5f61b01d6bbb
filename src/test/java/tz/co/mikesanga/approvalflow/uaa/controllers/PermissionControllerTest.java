package tz.co.mikesanga.approvalflow.uaa.controllers;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;
import tz.co.mikesanga.approvalflow.global.dtos.GqlResponseDto;
import tz.co.mikesanga.approvalflow.global.dtos.ResponseCode;
import tz.co.mikesanga.approvalflow.uaa.dtos.PermissionResponseDto;
import tz.co.mikesanga.approvalflow.uaa.services.PermissionService;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class PermissionControllerTest {

    @Autowired
    private PermissionController permissionController;

    @Autowired
    private PermissionService permissionService;

    @Test
    public void testListPermissions() {
        // When
        GqlResponseDto<PermissionResponseDto> response = permissionController.listPermissions();

        // Then
        assertTrue(response.getStatus());
        assertEquals(ResponseCode.SUCCESS, response.getCode());
        assertNotNull(response.getDataList());
        
        // Verify that we have some permissions (from the seed data)
        assertFalse(response.getDataList().isEmpty());
        
        // Check that the permissions have the expected structure
        PermissionResponseDto firstPermission = response.getDataList().get(0);
        assertNotNull(firstPermission.getUuid());
        assertNotNull(firstPermission.getName());
        assertNotNull(firstPermission.getDescription());
        assertNotNull(firstPermission.getGroupName());
        
        // Verify that the permission names follow the expected pattern
        assertTrue(firstPermission.getName().startsWith("ROLE_"));
    }

    @Test
    public void testListPermissions_ContainsExpectedPermissions() {
        // When
        GqlResponseDto<PermissionResponseDto> response = permissionController.listPermissions();

        // Then
        assertTrue(response.getStatus());
        assertNotNull(response.getDataList());
        
        // Check that our newly added permission is included
        boolean hasPermissionsViewPermission = response.getDataList().stream()
                .anyMatch(permission -> "ROLE_PERMISSIONS_VIEW".equals(permission.getName()));
        
        assertTrue(hasPermissionsViewPermission, "Should contain ROLE_PERMISSIONS_VIEW permission");
        
        // Check that some standard permissions are included
        boolean hasUsersViewPermission = response.getDataList().stream()
                .anyMatch(permission -> "ROLE_USERS_VIEW".equals(permission.getName()));
        
        assertTrue(hasUsersViewPermission, "Should contain ROLE_USERS_VIEW permission");
    }
}
