package tz.co.mikesanga.approvalflow.uaa.services;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;
import tz.co.mikesanga.approvalflow.global.dtos.GqlResponseDto;
import tz.co.mikesanga.approvalflow.global.dtos.ResponseCode;
import tz.co.mikesanga.approvalflow.uaa.dtos.UserCreateRequestDto;
import tz.co.mikesanga.approvalflow.uaa.dtos.UserFilterDto;
import tz.co.mikesanga.approvalflow.uaa.dtos.UserResponseDto;
import tz.co.mikesanga.approvalflow.uaa.dtos.UserUpdateRequestDto;
import tz.co.mikesanga.approvalflow.uaa.entities.UserAccount;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class UserManagementServiceTest {

    @Autowired
    private UserManagementService userManagementService;

    @Test
    public void testCreateUser() {
        // Given
        UserCreateRequestDto createDto = new UserCreateRequestDto();
        createDto.setUsername("testuser");
        createDto.setPassword("password123");
        createDto.setFullName("Test User");
        createDto.setEmail("<EMAIL>");
        createDto.setPhone("**********");

        // When
        GqlResponseDto<UserResponseDto> response = userManagementService.createUser(createDto);

        // Then
        assertTrue(response.getStatus());
        assertEquals(ResponseCode.SUCCESS, response.getCode());
        assertNotNull(response.getData());
        assertEquals("testuser", response.getData().getUsername());
        assertEquals("Test User", response.getData().getFullName());
        assertEquals("<EMAIL>", response.getData().getEmail());
        assertEquals(UserAccount.UserStatus.ACTIVE, response.getData().getStatus());
    }

    @Test
    public void testCreateUserWithDuplicateUsername() {
        // Given
        UserCreateRequestDto createDto1 = new UserCreateRequestDto();
        createDto1.setUsername("duplicate");
        createDto1.setPassword("password123");
        createDto1.setFullName("User One");
        createDto1.setEmail("<EMAIL>");

        UserCreateRequestDto createDto2 = new UserCreateRequestDto();
        createDto2.setUsername("duplicate");
        createDto2.setPassword("password123");
        createDto2.setFullName("User Two");
        createDto2.setEmail("<EMAIL>");

        // When
        GqlResponseDto<UserResponseDto> response1 = userManagementService.createUser(createDto1);
        GqlResponseDto<UserResponseDto> response2 = userManagementService.createUser(createDto2);

        // Then
        assertTrue(response1.getStatus());
        assertFalse(response2.getStatus());
        assertEquals(ResponseCode.DUPLICATE, response2.getCode());
        assertEquals("Username already exists", response2.getErrorDescription());
    }

    @Test
    public void testGetAllUsers() {
        // Given
        UserCreateRequestDto createDto = new UserCreateRequestDto();
        createDto.setUsername("listuser");
        createDto.setPassword("password123");
        createDto.setFullName("List User");
        createDto.setEmail("<EMAIL>");

        userManagementService.createUser(createDto);

        UserFilterDto filterDto = new UserFilterDto();
        filterDto.setPage(0);
        filterDto.setSize(10);

        // When
        GqlResponseDto<UserResponseDto> response = userManagementService.getAllUsers(filterDto);

        // Then
        assertTrue(response.getStatus());
        assertEquals(ResponseCode.SUCCESS, response.getCode());
        assertNotNull(response.getDataList());
        assertFalse(response.getDataList().isEmpty());

        // Check pagination extras
        assertNotNull(response.getExtras());
        assertTrue(response.getExtras().containsKey("totalElements"));
        assertTrue(response.getExtras().containsKey("totalPages"));
    }

    @Test
    public void testUpdateUser() {
        // Given - Create a user first
        UserCreateRequestDto createDto = new UserCreateRequestDto();
        createDto.setUsername("updateuser");
        createDto.setPassword("password123");
        createDto.setFullName("Update User");
        createDto.setEmail("<EMAIL>");

        GqlResponseDto<UserResponseDto> createResponse = userManagementService.createUser(createDto);
        String userUuid = createResponse.getData().getUuid();

        // When - Update the user
        UserUpdateRequestDto updateDto = new UserUpdateRequestDto();
        updateDto.setUuid(userUuid);
        updateDto.setFullName("Updated User Name");
        updateDto.setEmail("<EMAIL>");
        updateDto.setStatus(UserAccount.UserStatus.INACTIVE);

        GqlResponseDto<UserResponseDto> updateResponse = userManagementService.updateUser(updateDto);

        // Then
        assertTrue(updateResponse.getStatus());
        assertEquals(ResponseCode.SUCCESS, updateResponse.getCode());
        assertEquals("Updated User Name", updateResponse.getData().getFullName());
        assertEquals("<EMAIL>", updateResponse.getData().getEmail());
        assertEquals(UserAccount.UserStatus.INACTIVE, updateResponse.getData().getStatus());
    }

    @Test
    public void testDeactivateAndReactivateUser() {
        // Given - Create a user first
        UserCreateRequestDto createDto = new UserCreateRequestDto();
        createDto.setUsername("statususer");
        createDto.setPassword("password123");
        createDto.setFullName("Status User");
        createDto.setEmail("<EMAIL>");

        GqlResponseDto<UserResponseDto> createResponse = userManagementService.createUser(createDto);
        String userUuid = createResponse.getData().getUuid();

        // When - Deactivate user
        GqlResponseDto<UserResponseDto> deactivateResponse = userManagementService.deactivateUser(userUuid);

        // Then
        assertTrue(deactivateResponse.getStatus());
        assertEquals(ResponseCode.SUCCESS, deactivateResponse.getCode());
        assertEquals(UserAccount.UserStatus.INACTIVE, deactivateResponse.getData().getStatus());

        // When - Reactivate user
        GqlResponseDto<UserResponseDto> reactivateResponse = userManagementService.reactivateUser(userUuid);

        // Then
        assertTrue(reactivateResponse.getStatus());
        assertEquals(ResponseCode.SUCCESS, reactivateResponse.getCode());
        assertEquals(UserAccount.UserStatus.ACTIVE, reactivateResponse.getData().getStatus());
    }

    @Test
    public void testGetUserByUuid() {
        // Given - Create a user first
        UserCreateRequestDto createDto = new UserCreateRequestDto();
        createDto.setUsername("getuser");
        createDto.setPassword("password123");
        createDto.setFullName("Get User");
        createDto.setEmail("<EMAIL>");

        GqlResponseDto<UserResponseDto> createResponse = userManagementService.createUser(createDto);
        String userUuid = createResponse.getData().getUuid();

        // When
        GqlResponseDto<UserResponseDto> getResponse = userManagementService.getUserByUuid(userUuid);

        // Then
        assertTrue(getResponse.getStatus());
        assertEquals(ResponseCode.SUCCESS, getResponse.getCode());
        assertNotNull(getResponse.getData());
        assertEquals("getuser", getResponse.getData().getUsername());
        assertEquals("Get User", getResponse.getData().getFullName());
    }

    @Test
    public void testGetNonExistentUser() {
        // When
        GqlResponseDto<UserResponseDto> response = userManagementService.getUserByUuid("00000000-0000-0000-0000-000000000000");

        // Then
        assertFalse(response.getStatus());
        assertEquals(ResponseCode.NO_RECORD_FOUND, response.getCode());
        assertEquals("User not found", response.getErrorDescription());
    }
}
